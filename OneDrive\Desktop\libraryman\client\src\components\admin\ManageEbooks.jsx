import React, { useState, useEffect } from 'react'
import { Plus, Upload, Download, Search, Edit, Trash2, Monitor } from 'lucide-react'
import axios from 'axios'

const ManageEbooks = () => {
  const [ebooks, setEbooks] = useState([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [showBulkUpload, setShowBulkUpload] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  const [formData, setFormData] = useState({
    access_no: '',
    title: '',
    author: '',
    publisher: '',
    department: '',
    category: '',
    file_format: '',
    file_size: ''
  })

  const [bulkFile, setBulkFile] = useState(null)

  useEffect(() => {
    fetchEbooks()
  }, [currentPage, searchTerm])

  const fetchEbooks = async () => {
    try {
      const response = await axios.get('/admin/ebooks', {
        params: {
          page: currentPage,
          per_page: 10,
          search: searchTerm
        }
      })
      setEbooks(response.data.ebooks)
      setTotalPages(response.data.pagination.pages)
    } catch (error) {
      console.error('Failed to fetch ebooks:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      await axios.post('/admin/ebooks', formData)
      alert('E-book added successfully!')
      setShowAddForm(false)
      setFormData({
        access_no: '',
        title: '',
        author: '',
        publisher: '',
        department: '',
        category: '',
        file_format: '',
        file_size: ''
      })
      fetchEbooks()
    } catch (error) {
      alert(error.response?.data?.error || 'Failed to add e-book')
    }
  }

  const downloadSample = async () => {
    try {
      const response = await axios.get('/admin/ebooks/sample', {
        responseType: 'blob'
      })

      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', 'ebooks_sample.xlsx')
      document.body.appendChild(link)
      link.click()
      link.remove()
    } catch (error) {
      alert('Failed to download sample file')
    }
  }

  const handleBulkUpload = async (e) => {
    e.preventDefault()
    if (!bulkFile) {
      alert('Please select a file')
      return
    }

    const formData = new FormData()
    formData.append('file', bulkFile)

    try {
      const response = await axios.post('/admin/ebooks/bulk', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      alert(`Successfully added ${response.data.created_ebooks.length} e-books`)
      setShowBulkUpload(false)
      setBulkFile(null)
      fetchEbooks()
    } catch (error) {
      alert(error.response?.data?.error || 'Failed to upload e-books')
    }
  }

  if (loading) {
    return <div className="loading">Loading e-books...</div>
  }

  return (
    <div className="manage-ebooks">
      <div className="page-header">
        <div>
          <h1>Manage E-books</h1>
          <p>Add and manage digital book collection</p>
        </div>
        <div className="header-actions">
          <button
            className="btn btn-primary"
            onClick={() => setShowAddForm(true)}
          >
            <Plus size={16} />
            Add E-book
          </button>
          <button
            className="btn btn-secondary"
            onClick={() => setShowBulkUpload(true)}
          >
            <Upload size={16} />
            Bulk Upload
          </button>
          <button
            className="btn btn-secondary"
            onClick={downloadSample}
          >
            <Download size={16} />
            Sample Sheet
          </button>
        </div>
      </div>

      <div className="filters">
        <div className="search-box">
          <Search size={16} />
          <input
            type="text"
            placeholder="Search e-books..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <div className="ebooks-table">
        <table>
          <thead>
            <tr>
              <th>Access No</th>
              <th>Title</th>
              <th>Author</th>
              <th>Publisher</th>
              <th>Department</th>
              <th>Category</th>
              <th>Format</th>
              <th>Size</th>
              <th>Downloads</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {ebooks.map((ebook) => (
              <tr key={ebook.id}>
                <td>{ebook.access_no}</td>
                <td>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                    <Monitor size={16} color="#667eea" />
                    {ebook.title}
                  </div>
                </td>
                <td>{ebook.author}</td>
                <td>{ebook.publisher}</td>
                <td>{ebook.department}</td>
                <td>{ebook.category}</td>
                <td>{ebook.file_format}</td>
                <td>{ebook.file_size}</td>
                <td>{ebook.download_count}</td>
                <td>
                  <div className="actions">
                    <button className="btn-icon">
                      <Edit size={14} />
                    </button>
                    <button className="btn-icon danger">
                      <Trash2 size={14} />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="pagination">
        <button
          disabled={currentPage === 1}
          onClick={() => setCurrentPage(currentPage - 1)}
        >
          Previous
        </button>
        <span>Page {currentPage} of {totalPages}</span>
        <button
          disabled={currentPage === totalPages}
          onClick={() => setCurrentPage(currentPage + 1)}
        >
          Next
        </button>
      </div>

      {/* Add E-book Modal */}
      {showAddForm && (
        <div className="modal">
          <div className="modal-content">
            <div className="modal-header">
              <h2>Add New E-book</h2>
              <button onClick={() => setShowAddForm(false)}>×</button>
            </div>
            <form onSubmit={handleSubmit}>
              <div className="form-grid">
                <div className="form-group">
                  <label>Access Number</label>
                  <input
                    type="text"
                    name="access_no"
                    value={formData.access_no}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="form-group">
                  <label>E-book Title</label>
                  <input
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="form-group">
                  <label>Author</label>
                  <input
                    type="text"
                    name="author"
                    value={formData.author}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="form-group">
                  <label>Publisher</label>
                  <input
                    type="text"
                    name="publisher"
                    value={formData.publisher}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="form-group">
                  <label>Department</label>
                  <input
                    type="text"
                    name="department"
                    value={formData.department}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="form-group">
                  <label>Category</label>
                  <input
                    type="text"
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="form-group">
                  <label>File Format</label>
                  <select
                    name="file_format"
                    value={formData.file_format}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select Format</option>
                    <option value="PDF">PDF</option>
                    <option value="EPUB">EPUB</option>
                    <option value="MOBI">MOBI</option>
                    <option value="DOC">DOC</option>
                    <option value="DOCX">DOCX</option>
                  </select>
                </div>
                <div className="form-group">
                  <label>File Size</label>
                  <input
                    type="text"
                    name="file_size"
                    value={formData.file_size}
                    onChange={handleInputChange}
                    placeholder="e.g., 15MB"
                  />
                </div>
              </div>
              <div className="modal-actions">
                <button type="button" onClick={() => setShowAddForm(false)}>
                  Cancel
                </button>
                <button type="submit" className="btn btn-primary">
                  Add E-book
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Bulk Upload Modal */}
      {showBulkUpload && (
        <div className="modal">
          <div className="modal-content">
            <div className="modal-header">
              <h2>Bulk Upload E-books</h2>
              <button onClick={() => setShowBulkUpload(false)}>×</button>
            </div>
            <form onSubmit={handleBulkUpload}>
              <div className="form-group">
                <label>Excel File</label>
                <input
                  type="file"
                  accept=".xlsx,.xls"
                  onChange={(e) => setBulkFile(e.target.files[0])}
                  required
                />
                <small>
                  Excel file should contain columns: access_no, title, author, publisher, department, category, file_format, file_size
                </small>
              </div>
              <div className="modal-actions">
                <button type="button" onClick={() => setShowBulkUpload(false)}>
                  Cancel
                </button>
                <button type="button" onClick={downloadSample} className="btn btn-secondary">
                  Download Sample
                </button>
                <button type="submit" className="btn btn-primary">
                  Upload E-books
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}

export default ManageEbooks
