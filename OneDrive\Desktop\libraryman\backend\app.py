from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import J<PERSON><PERSON>ana<PERSON>, create_access_token, jwt_required, get_jwt_identity, get_jwt
from flask_cors import CORS
from flask_migrate import Migrate
from dotenv import load_dotenv
from werkzeug.security import generate_password_hash, check_password_hash
import os
from datetime import datetime, timedelta

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'your-secret-key-here')
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'sqlite:///library.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'jwt-secret-string')

# Initialize extensions
db = SQLAlchemy(app)
jwt = JWTManager(app)
cors = CORS(app)
migrate = Migrate(app, db)

# Define models inline
class College(db.Model):
    __tablename__ = 'colleges'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    code = db.Column(db.String(10), nullable=False, unique=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    departments = db.relationship('Department', backref='college', lazy=True, cascade='all, delete-orphan')
    users = db.relationship('User', backref='college', lazy=True)

class Department(db.Model):
    __tablename__ = 'departments'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(10), nullable=False)
    college_id = db.Column(db.Integer, db.ForeignKey('colleges.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    users = db.relationship('User', backref='department', lazy=True)

    __table_args__ = (db.UniqueConstraint('name', 'college_id'),)

class User(db.Model):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.String(50), nullable=False, unique=True)
    username = db.Column(db.String(80), nullable=False, unique=True)
    password_hash = db.Column(db.String(120), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), nullable=False, unique=True)
    role = db.Column(db.String(20), nullable=False)
    user_role = db.Column(db.String(20), default='student')  # student, staff
    designation = db.Column(db.String(50), nullable=False)
    dob = db.Column(db.Date, nullable=False)
    validity_date = db.Column(db.Date, nullable=False)
    college_id = db.Column(db.Integer, db.ForeignKey('colleges.id'), nullable=True)
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'), nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    @staticmethod
    def generate_username(name, user_id):
        """Generate username from name + user_id"""
        # Remove spaces and special characters from name, take first part
        clean_name = ''.join(c for c in name.split()[0] if c.isalnum()).lower()
        return f"{clean_name}{user_id}"

    @staticmethod
    def generate_password(length=8):
        """Generate random password"""
        import string
        import random
        characters = string.ascii_letters + string.digits
        return ''.join(random.choice(characters) for _ in range(length))

# Book and other models (enhanced)
class Book(db.Model):
    __tablename__ = 'books'

    id = db.Column(db.Integer, primary_key=True)
    access_no = db.Column(db.String(50), nullable=False, unique=True)
    title = db.Column(db.String(200), nullable=False)
    author = db.Column(db.String(200), nullable=False)
    publisher = db.Column(db.String(100))
    department = db.Column(db.String(100))
    category = db.Column(db.String(50))
    location = db.Column(db.String(50))
    number_of_copies = db.Column(db.Integer, default=1)
    available_copies = db.Column(db.Integer, default=1)
    isbn = db.Column(db.String(20))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Ebook(db.Model):
    __tablename__ = 'ebooks'

    id = db.Column(db.Integer, primary_key=True)
    access_no = db.Column(db.String(50), nullable=False, unique=True)
    title = db.Column(db.String(200), nullable=False)
    author = db.Column(db.String(200), nullable=False)
    publisher = db.Column(db.String(100))
    department = db.Column(db.String(100))
    category = db.Column(db.String(50))
    file_format = db.Column(db.String(10))  # PDF, EPUB, etc.
    file_size = db.Column(db.String(20))
    file_path = db.Column(db.String(500))
    download_count = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Circulation(db.Model):
    __tablename__ = 'circulations'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    book_id = db.Column(db.Integer, db.ForeignKey('books.id'), nullable=False)
    issue_date = db.Column(db.DateTime, default=datetime.utcnow)
    due_date = db.Column(db.DateTime, nullable=False)
    return_date = db.Column(db.DateTime)
    status = db.Column(db.String(20), default='issued')  # issued, returned, overdue
    fine_amount = db.Column(db.Float, default=0.0)

    # Relationships
    user = db.relationship('User', backref='circulations')
    book = db.relationship('Book', backref='circulations')

class Fine(db.Model):
    __tablename__ = 'fines'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    circulation_id = db.Column(db.Integer, db.ForeignKey('circulations.id'), nullable=True)
    amount = db.Column(db.Float, nullable=False)
    reason = db.Column(db.String(200), nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, paid
    created_date = db.Column(db.DateTime, default=datetime.utcnow)
    paid_date = db.Column(db.DateTime)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # Relationships
    user = db.relationship('User', foreign_keys=[user_id], backref='fines')
    circulation = db.relationship('Circulation', backref='fines')
    created_by_user = db.relationship('User', foreign_keys=[created_by])

class NewsClipping(db.Model):
    __tablename__ = 'news_clippings'

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    content = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

# Gate Entry Credentials model
class GateEntryCredential(db.Model):
    __tablename__ = 'gate_entry_credentials'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(100), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    name = db.Column(db.String(200), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_date = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # Relationships
    created_by_user = db.relationship('User', backref='gate_credentials_created')

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

# Gate Entry Log model
class GateEntryLog(db.Model):
    __tablename__ = 'gate_entry_logs'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    entry_time = db.Column(db.DateTime, nullable=True)
    exit_time = db.Column(db.DateTime, nullable=True)
    status = db.Column(db.String(20), default='in')  # in, out
    scanned_by = db.Column(db.Integer, db.ForeignKey('gate_entry_credentials.id'), nullable=False)
    created_date = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    user = db.relationship('User', backref='gate_logs')
    scanned_by_credential = db.relationship('GateEntryCredential', backref='scanned_logs')

# Note: Blueprint imports commented out due to circular import issues
# Will add routes directly to app for now
# from routes.admin import admin_bp
# from routes.auth import auth_bp
# from routes.student import student_bp
# from routes.librarian import librarian_bp

# Register blueprints
# app.register_blueprint(admin_bp, url_prefix='/api/admin')
# app.register_blueprint(auth_bp, url_prefix='/api/auth')
# app.register_blueprint(student_bp, url_prefix='/api/student')
# app.register_blueprint(librarian_bp, url_prefix='/api/librarian')

# Basic authentication route
@app.route('/api/auth/login', methods=['POST'])
def login():
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')

        if not username or not password:
            return jsonify({'error': 'Username and password required'}), 400

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password) and user.is_active:
            # Check if user's validity date has not expired
            from datetime import date
            if user.validity_date < date.today():
                return jsonify({'error': 'Account has expired'}), 401

            access_token = create_access_token(
                identity=str(user.id),
                expires_delta=timedelta(hours=24)
            )

            return jsonify({
                'access_token': access_token,
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'name': user.name,
                    'email': user.email,
                    'role': user.role,
                    'designation': user.designation,
                    'college_id': user.college_id,
                    'department_id': user.department_id
                }
            }), 200

        return jsonify({'error': 'Invalid credentials'}), 401

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/auth/profile', methods=['GET'])
@jwt_required()
def get_profile():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)

        if not user:
            return jsonify({'error': 'User not found'}), 404

        return jsonify({
            'user': {
                'id': user.id,
                'user_id': user.user_id,
                'username': user.username,
                'name': user.name,
                'email': user.email,
                'role': user.role,
                'designation': user.designation,
                'dob': user.dob.isoformat() if user.dob else None,
                'validity_date': user.validity_date.isoformat() if user.validity_date else None,
                'college': user.college.name if user.college else None,
                'department': user.department.name if user.department else None,
                'created_at': user.created_at.isoformat()
            }
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Book Management Routes
@app.route('/api/admin/books', methods=['GET'])
@jwt_required()
def get_books():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        search = request.args.get('search', '')

        query = Book.query
        if search:
            query = query.filter(
                Book.title.contains(search) |
                Book.author.contains(search) |
                Book.access_no.contains(search)
            )

        books = query.paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            'books': [{
                'id': book.id,
                'access_no': book.access_no,
                'title': book.title,
                'author': book.author,
                'publisher': book.publisher,
                'department': book.department,
                'category': book.category,
                'location': book.location,
                'number_of_copies': book.number_of_copies,
                'available_copies': book.available_copies,
                'isbn': book.isbn,
                'created_at': book.created_at.isoformat()
            } for book in books.items],
            'pagination': {
                'page': books.page,
                'pages': books.pages,
                'per_page': books.per_page,
                'total': books.total
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/books', methods=['POST'])
@jwt_required()
def create_book():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        data = request.get_json()

        # Check if access number already exists
        existing = Book.query.filter_by(access_no=data.get('access_no')).first()
        if existing:
            return jsonify({'error': 'Access number already exists'}), 400

        book = Book(
            access_no=data.get('access_no'),
            title=data.get('title'),
            author=data.get('author'),
            publisher=data.get('publisher'),
            department=data.get('department'),
            category=data.get('category'),
            location=data.get('location'),
            number_of_copies=data.get('number_of_copies', 1),
            available_copies=data.get('number_of_copies', 1),
            isbn=data.get('isbn')
        )

        db.session.add(book)
        db.session.commit()

        return jsonify({
            'message': 'Book created successfully',
            'book': {
                'id': book.id,
                'access_no': book.access_no,
                'title': book.title,
                'author': book.author
            }
        }), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/books/bulk', methods=['POST'])
@jwt_required()
def bulk_create_books():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        if 'file' not in request.files:
            return jsonify({'error': 'No file uploaded'}), 400

        file = request.files['file']

        # Read Excel file
        try:
            import pandas as pd
            df = pd.read_excel(file)
        except ImportError:
            return jsonify({'error': 'pandas library not installed'}), 500

        # Validate required columns
        required_columns = ['access_no', 'title', 'author', 'publisher', 'department', 'category', 'location', 'number_of_copies']
        if not all(col in df.columns for col in required_columns):
            return jsonify({'error': f'Excel file must contain columns: {required_columns}'}), 400

        created_books = []
        errors = []

        for index, row in df.iterrows():
            try:
                access_no = str(row['access_no'])

                # Check if book already exists
                existing = Book.query.filter_by(access_no=access_no).first()
                if existing:
                    errors.append(f"Row {index + 1}: Access number {access_no} already exists")
                    continue

                book = Book(
                    access_no=access_no,
                    title=row['title'],
                    author=row['author'],
                    publisher=row['publisher'],
                    department=row['department'],
                    category=row['category'],
                    location=row['location'],
                    number_of_copies=int(row['number_of_copies']),
                    available_copies=int(row['number_of_copies']),
                    isbn=row.get('isbn', '')
                )

                db.session.add(book)
                created_books.append({
                    'access_no': access_no,
                    'title': row['title'],
                    'author': row['author']
                })

            except Exception as e:
                errors.append(f"Row {index + 1}: {str(e)}")

        db.session.commit()

        return jsonify({
            'message': f'Successfully created {len(created_books)} books',
            'created_books': created_books,
            'errors': errors
        }), 201

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/books/sample', methods=['GET'])
@jwt_required()
def download_books_sample():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        import pandas as pd
        import io
        from flask import send_file

        # Create sample data
        sample_data = {
            'access_no': ['B001', 'B002', 'B003'],
            'title': ['Introduction to Computer Science', 'Data Structures and Algorithms', 'Database Management Systems'],
            'author': ['John Smith', 'Jane Doe', 'Robert Johnson'],
            'publisher': ['Tech Publications', 'Academic Press', 'University Books'],
            'department': ['Computer Science', 'Computer Science', 'Information Technology'],
            'category': ['Textbook', 'Reference', 'Textbook'],
            'location': ['A1-S1', 'A1-S2', 'A2-S1'],
            'number_of_copies': [5, 3, 4],
            'isbn': ['978-0123456789', '978-0987654321', '978-0456789123']
        }

        df = pd.DataFrame(sample_data)

        # Create Excel file in memory
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Books Sample', index=False)

        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name='books_sample.xlsx'
        )

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Update Book
@app.route('/api/admin/books/<int:book_id>', methods=['PUT'])
@jwt_required()
def update_book(book_id):
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        book = Book.query.get(book_id)
        if not book:
            return jsonify({'error': 'Book not found'}), 404

        data = request.get_json()
        access_no = data.get('access_no')
        title = data.get('title')
        author = data.get('author')
        publisher = data.get('publisher')
        department = data.get('department')
        category = data.get('category')
        location = data.get('location')
        number_of_copies = data.get('number_of_copies', 1)
        isbn = data.get('isbn')

        if not all([access_no, title, author]):
            return jsonify({'error': 'Access number, title, and author are required'}), 400

        # Check if another book with same access number exists
        existing = Book.query.filter(Book.access_no == access_no, Book.id != book_id).first()
        if existing:
            return jsonify({'error': 'Access number already exists'}), 400

        # Calculate available copies change
        copies_diff = int(number_of_copies) - book.number_of_copies
        new_available = book.available_copies + copies_diff

        if new_available < 0:
            return jsonify({'error': 'Cannot reduce copies below issued books count'}), 400

        book.access_no = access_no
        book.title = title
        book.author = author
        book.publisher = publisher
        book.department = department
        book.category = category
        book.location = location
        book.number_of_copies = int(number_of_copies)
        book.available_copies = new_available
        book.isbn = isbn

        db.session.commit()

        return jsonify({
            'message': 'Book updated successfully',
            'book': {
                'id': book.id,
                'access_no': book.access_no,
                'title': book.title,
                'author': book.author
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Delete Book
@app.route('/api/admin/books/<int:book_id>', methods=['DELETE'])
@jwt_required()
def delete_book(book_id):
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        book = Book.query.get(book_id)
        if not book:
            return jsonify({'error': 'Book not found'}), 404

        # Check if book is currently issued
        if book.available_copies < book.number_of_copies:
            return jsonify({'error': 'Cannot delete book that is currently issued'}), 400

        db.session.delete(book)
        db.session.commit()

        return jsonify({'message': 'Book deleted successfully'}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Basic admin routes for testing
@app.route('/api/admin/colleges', methods=['GET'])
@jwt_required()
def get_colleges():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        colleges = College.query.all()
        return jsonify({
            'colleges': [{
                'id': college.id,
                'name': college.name,
                'code': college.code,
                'created_at': college.created_at.isoformat(),
                'departments_count': len(college.departments)
            } for college in colleges]
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/colleges', methods=['POST'])
@jwt_required()
def create_college():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        data = request.get_json()
        name = data.get('name')
        code = data.get('code')

        if not name or not code:
            return jsonify({'error': 'Name and code are required'}), 400

        # Check if college already exists
        existing = College.query.filter_by(name=name).first()
        if existing:
            return jsonify({'error': 'College already exists'}), 400

        college = College(name=name, code=code)
        db.session.add(college)
        db.session.commit()

        return jsonify({
            'message': 'College created successfully',
            'college': {
                'id': college.id,
                'name': college.name,
                'code': college.code
            }
        }), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Update College
@app.route('/api/admin/colleges/<int:college_id>', methods=['PUT'])
@jwt_required()
def update_college(college_id):
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        college = College.query.get(college_id)
        if not college:
            return jsonify({'error': 'College not found'}), 404

        data = request.get_json()
        name = data.get('name')
        code = data.get('code')

        if not name or not code:
            return jsonify({'error': 'Name and code are required'}), 400

        # Check if another college with same name exists
        existing = College.query.filter(College.name == name, College.id != college_id).first()
        if existing:
            return jsonify({'error': 'College name already exists'}), 400

        # Check if another college with same code exists
        existing_code = College.query.filter(College.code == code, College.id != college_id).first()
        if existing_code:
            return jsonify({'error': 'College code already exists'}), 400

        college.name = name
        college.code = code
        db.session.commit()

        return jsonify({
            'message': 'College updated successfully',
            'college': {
                'id': college.id,
                'name': college.name,
                'code': college.code
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Delete College
@app.route('/api/admin/colleges/<int:college_id>', methods=['DELETE'])
@jwt_required()
def delete_college(college_id):
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        college = College.query.get(college_id)
        if not college:
            return jsonify({'error': 'College not found'}), 404

        # Check if college has departments
        if college.departments:
            return jsonify({'error': 'Cannot delete college with existing departments'}), 400

        # Check if college has users
        if college.users:
            return jsonify({'error': 'Cannot delete college with existing users'}), 400

        db.session.delete(college)
        db.session.commit()

        return jsonify({'message': 'College deleted successfully'}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/departments', methods=['GET'])
@jwt_required()
def get_departments():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        college_id = request.args.get('college_id')
        if college_id:
            departments = Department.query.filter_by(college_id=college_id).all()
        else:
            departments = Department.query.all()

        return jsonify({
            'departments': [{
                'id': dept.id,
                'name': dept.name,
                'code': dept.code,
                'college_id': dept.college_id,
                'college_name': dept.college.name,
                'created_at': dept.created_at.isoformat()
            } for dept in departments]
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/departments', methods=['POST'])
@jwt_required()
def create_department():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        data = request.get_json()
        name = data.get('name')
        code = data.get('code')
        college_id = data.get('college_id')

        if not name or not code or not college_id:
            return jsonify({'error': 'Name, code, and college_id are required'}), 400

        # Check if college exists
        college = College.query.get(college_id)
        if not college:
            return jsonify({'error': 'College not found'}), 404

        # Check if department already exists in this college
        existing = Department.query.filter_by(name=name, college_id=college_id).first()
        if existing:
            return jsonify({'error': 'Department already exists in this college'}), 400

        department = Department(name=name, code=code, college_id=college_id)
        db.session.add(department)
        db.session.commit()

        return jsonify({
            'message': 'Department created successfully',
            'department': {
                'id': department.id,
                'name': department.name,
                'code': department.code,
                'college_id': department.college_id
            }
        }), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Update Department
@app.route('/api/admin/departments/<int:department_id>', methods=['PUT'])
@jwt_required()
def update_department(department_id):
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        department = Department.query.get(department_id)
        if not department:
            return jsonify({'error': 'Department not found'}), 404

        data = request.get_json()
        name = data.get('name')
        code = data.get('code')
        college_id = data.get('college_id')

        if not name or not code or not college_id:
            return jsonify({'error': 'Name, code, and college are required'}), 400

        # Check if college exists
        college = College.query.get(college_id)
        if not college:
            return jsonify({'error': 'College not found'}), 404

        # Check if another department with same name exists in the same college
        existing = Department.query.filter(
            Department.name == name,
            Department.college_id == college_id,
            Department.id != department_id
        ).first()
        if existing:
            return jsonify({'error': 'Department name already exists in this college'}), 400

        department.name = name
        department.code = code
        department.college_id = college_id
        db.session.commit()

        return jsonify({
            'message': 'Department updated successfully',
            'department': {
                'id': department.id,
                'name': department.name,
                'code': department.code,
                'college_id': department.college_id
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Delete Department
@app.route('/api/admin/departments/<int:department_id>', methods=['DELETE'])
@jwt_required()
def delete_department(department_id):
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        department = Department.query.get(department_id)
        if not department:
            return jsonify({'error': 'Department not found'}), 404

        # Check if department has users
        if department.users:
            return jsonify({'error': 'Cannot delete department with existing users'}), 400

        db.session.delete(department)
        db.session.commit()

        return jsonify({'message': 'Department deleted successfully'}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# User Management Routes
@app.route('/api/admin/users', methods=['GET'])
@jwt_required()
def get_users():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        role = request.args.get('role')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        search = request.args.get('search', '')

        query = User.query
        if role:
            query = query.filter_by(role=role)
        if search:
            query = query.filter(
                User.name.contains(search) |
                User.email.contains(search) |
                User.user_id.contains(search)
            )

        users = query.paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            'users': [{
                'id': user.id,
                'user_id': user.user_id,
                'username': user.username,
                'name': user.name,
                'email': user.email,
                'role': user.role,
                'designation': user.designation,
                'dob': user.dob.isoformat() if user.dob else None,
                'validity_date': user.validity_date.isoformat() if user.validity_date else None,
                'college': user.college.name if user.college else None,
                'department': user.department.name if user.department else None,
                'is_active': user.is_active,
                'created_at': user.created_at.isoformat()
            } for user in users.items],
            'pagination': {
                'page': users.page,
                'pages': users.pages,
                'per_page': users.per_page,
                'total': users.total
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/users', methods=['POST'])
@jwt_required()
def create_user():
    try:
        user_id_jwt = int(get_jwt_identity())
        current_user = User.query.get(user_id_jwt)
        if not current_user or current_user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        data = request.get_json()
        user_id = data.get('user_id')  # Roll number
        name = data.get('name')
        email = data.get('email')
        college_id = data.get('college_id')
        department_id = data.get('department_id')
        designation = data.get('designation')  # student or staff
        user_role = data.get('user_role', 'student')  # student or staff
        dob = data.get('dob')
        validity_date = data.get('validity_date')
        role = data.get('role', 'student')  # Default to student
        custom_username = data.get('username')  # Custom username for librarians
        custom_password = data.get('password')  # Custom password for librarians

        if not all([user_id, name, email, designation, dob, validity_date]):
            return jsonify({'error': 'All fields are required'}), 400

        # Check if user already exists
        existing = User.query.filter_by(user_id=user_id).first()
        if existing:
            return jsonify({'error': 'User ID already exists'}), 400

        existing_email = User.query.filter_by(email=email).first()
        if existing_email:
            return jsonify({'error': 'Email already exists'}), 400

        # Use custom username if provided (for librarians), otherwise use email
        if custom_username:
            username = custom_username
            # Check if username already exists
            existing_username = User.query.filter_by(username=username).first()
            if existing_username:
                return jsonify({'error': 'Username already exists'}), 400
        else:
            username = email  # Username should be email address

        # Use custom password if provided (for librarians), otherwise generate using email + user_id
        if custom_password:
            password = custom_password
        else:
            password = f"{email}{user_id}"  # Password format: email + user_id

        # Parse dates
        dob_date = datetime.strptime(dob, '%Y-%m-%d').date()
        validity_date_obj = datetime.strptime(validity_date, '%Y-%m-%d').date()

        user = User(
            user_id=user_id,
            username=username,
            name=name,
            email=email,
            role=role,
            user_role=user_role,
            designation=designation,
            dob=dob_date,
            validity_date=validity_date_obj,
            college_id=college_id,
            department_id=department_id
        )
        user.set_password(password)

        db.session.add(user)
        db.session.commit()

        return jsonify({
            'message': 'User created successfully',
            'user': {
                'id': user.id,
                'user_id': user.user_id,
                'username': username,
                'password': password,  # Return password for admin to share
                'name': user.name,
                'email': user.email,
                'role': user.role
            }
        }), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Bulk User Upload
@app.route('/api/admin/users/bulk', methods=['POST'])
@jwt_required()
def bulk_create_users():
    try:
        user_id_jwt = int(get_jwt_identity())
        current_user = User.query.get(user_id_jwt)
        if not current_user or current_user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        if 'file' not in request.files:
            return jsonify({'error': 'No file uploaded'}), 400

        file = request.files['file']
        college_id = request.form.get('college_id')
        department_id = request.form.get('department_id')
        user_role = request.form.get('user_role', 'student')

        if not college_id or not department_id:
            return jsonify({'error': 'College and department are required'}), 400

        # Read Excel file
        import pandas as pd
        df = pd.read_excel(file)

        # Validate required columns
        required_columns = ['user_id', 'name', 'email', 'validity_date', 'dob']
        if not all(col in df.columns for col in required_columns):
            return jsonify({'error': f'Excel file must contain columns: {required_columns}'}), 400

        created_users = []
        errors = []

        for index, row in df.iterrows():
            try:
                user_id = str(row['user_id'])
                name = row['name']
                email = row['email']
                dob = pd.to_datetime(row['dob']).date()
                validity_date = pd.to_datetime(row['validity_date']).date()

                # Check if user already exists
                existing = User.query.filter_by(user_id=user_id).first()
                if existing:
                    errors.append(f"Row {index + 1}: User ID {user_id} already exists")
                    continue

                # Generate username and password according to requirements
                # Username should be the email address
                username = email
                # Password should be username + user_id (concatenated, no spaces)
                password = f"{email}{user_id}"

                user = User(
                    user_id=user_id,
                    username=username,
                    name=name,
                    email=email,
                    role='student',
                    user_role=user_role,
                    designation=user_role,
                    dob=dob,
                    validity_date=validity_date,
                    college_id=college_id,
                    department_id=department_id
                )
                user.set_password(password)

                db.session.add(user)
                created_users.append({
                    'user_id': user_id,
                    'username': username,
                    'password': password,
                    'name': name,
                    'email': email
                })

            except Exception as e:
                errors.append(f"Row {index + 1}: {str(e)}")

        db.session.commit()

        return jsonify({
            'message': f'Successfully created {len(created_users)} users',
            'created_users': created_users,
            'errors': errors
        }), 201

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Download Credentials
@app.route('/api/admin/users/credentials', methods=['POST'])
@jwt_required()
def download_credentials():
    try:
        user_id_jwt = int(get_jwt_identity())
        current_user = User.query.get(user_id_jwt)
        if not current_user or current_user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        data = request.get_json()
        users_data = data.get('users', [])

        if not users_data:
            return jsonify({'error': 'No user data provided'}), 400

        # Create DataFrame with required columns: Student ID, Name, Email, Username, Password
        import pandas as pd
        import io
        from flask import send_file

        # Reformat data to match required columns
        formatted_data = []
        for user in users_data:
            formatted_data.append({
                'Student ID': user['user_id'],
                'Name': user['name'],
                'Email': user['email'],
                'Username': user['username'],
                'Password': user['password']
            })

        df = pd.DataFrame(formatted_data)

        # Create Excel file in memory
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Student Credentials', index=False)

        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'student_credentials_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        )

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Circulation Management Routes

# Get user circulation info (for issue/return forms)
@app.route('/api/admin/circulation/user/<user_id>', methods=['GET'])
@jwt_required()
def get_user_circulation_info(user_id):
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        # Find user by user_id (roll number) or database id
        user = User.query.filter_by(user_id=user_id).first()
        if not user:
            user = User.query.get(user_id)

        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Get current borrowed books
        current_books = db.session.query(Circulation, Book).join(Book).filter(
            Circulation.user_id == user.id,
            Circulation.status == 'issued'
        ).all()

        # Get borrowing history
        history = db.session.query(Circulation, Book).join(Book).filter(
            Circulation.user_id == user.id,
            Circulation.status.in_(['returned', 'overdue'])
        ).order_by(Circulation.issue_date.desc()).limit(10).all()

        # Calculate current fine amount
        total_fine = db.session.query(db.func.sum(Fine.amount)).filter(
            Fine.user_id == user.id,
            Fine.status == 'pending'
        ).scalar() or 0

        # Calculate overdue fines for current books
        from datetime import date
        today = date.today()
        daily_fine_rate = 1.0  # $1 per day

        current_books_data = []
        for circulation, book in current_books:
            days_overdue = 0
            is_overdue = False
            if circulation.due_date.date() < today:
                days_overdue = (today - circulation.due_date.date()).days
                is_overdue = True
                # Update circulation status if overdue
                if circulation.status != 'overdue':
                    circulation.status = 'overdue'
                    db.session.commit()

            current_books_data.append({
                'circulation_id': circulation.id,
                'book_id': book.id,
                'access_no': book.access_no,
                'title': book.title,
                'author': book.author,
                'issue_date': circulation.issue_date.isoformat(),
                'due_date': circulation.due_date.isoformat(),
                'is_overdue': is_overdue,
                'days_overdue': days_overdue,
                'fine_amount': days_overdue * daily_fine_rate if is_overdue else 0
            })

        history_data = []
        for circulation, book in history:
            history_data.append({
                'book_title': book.title,
                'author': book.author,
                'issue_date': circulation.issue_date.isoformat(),
                'due_date': circulation.due_date.isoformat(),
                'return_date': circulation.return_date.isoformat() if circulation.return_date else None,
                'status': circulation.status,
                'fine_amount': circulation.fine_amount
            })

        return jsonify({
            'user': {
                'id': user.id,
                'user_id': user.user_id,
                'name': user.name,
                'email': user.email,
                'college': user.college.name if user.college else None,
                'department': user.department.name if user.department else None
            },
            'current_books': current_books_data,
            'borrowing_history': history_data,
            'total_fine': total_fine,
            'can_borrow': total_fine == 0  # Can't borrow if there are outstanding fines
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Issue Book
@app.route('/api/admin/circulation/issue', methods=['POST'])
@jwt_required()
def issue_book():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        data = request.get_json()
        user_id = data.get('user_id')  # Roll number
        book_id = data.get('book_id')
        due_date = data.get('due_date')

        if not all([user_id, book_id, due_date]):
            return jsonify({'error': 'User ID, Book ID, and Due Date are required'}), 400

        # Find user
        user = User.query.filter_by(user_id=user_id).first()
        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Check if user has outstanding fines
        outstanding_fines = db.session.query(db.func.sum(Fine.amount)).filter(
            Fine.user_id == user.id,
            Fine.status == 'pending'
        ).scalar() or 0

        if outstanding_fines > 0:
            return jsonify({'error': f'User has outstanding fines of ${outstanding_fines:.2f}. Please clear fines before issuing books.'}), 400

        # Find book
        book = Book.query.get(book_id)
        if not book:
            return jsonify({'error': 'Book not found'}), 404

        # Check if book is available
        if book.available_copies <= 0:
            return jsonify({'error': 'Book is not available for issue'}), 400

        # Create circulation record
        circulation = Circulation(
            user_id=user.id,
            book_id=book.id,
            due_date=datetime.strptime(due_date, '%Y-%m-%d'),
            status='issued'
        )

        # Update book availability
        book.available_copies -= 1

        db.session.add(circulation)
        db.session.commit()

        return jsonify({
            'message': 'Book issued successfully',
            'circulation': {
                'id': circulation.id,
                'user_name': user.name,
                'book_title': book.title,
                'issue_date': circulation.issue_date.isoformat(),
                'due_date': circulation.due_date.isoformat()
            }
        }), 201

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Return Book
@app.route('/api/admin/circulation/return', methods=['POST'])
@jwt_required()
def return_book():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        data = request.get_json()
        circulation_ids = data.get('circulation_ids', [])

        if not circulation_ids:
            return jsonify({'error': 'No circulation IDs provided'}), 400

        returned_books = []
        total_fine = 0
        daily_fine_rate = 1.0  # $1 per day

        for circulation_id in circulation_ids:
            circulation = Circulation.query.get(circulation_id)
            if not circulation:
                continue

            if circulation.status != 'issued' and circulation.status != 'overdue':
                continue

            # Calculate fine if overdue
            from datetime import date
            today = date.today()
            fine_amount = 0

            if circulation.due_date.date() < today:
                days_overdue = (today - circulation.due_date.date()).days
                fine_amount = days_overdue * daily_fine_rate

            # Update circulation
            circulation.return_date = datetime.utcnow()
            circulation.status = 'returned'
            circulation.fine_amount = fine_amount

            # Update book availability
            book = Book.query.get(circulation.book_id)
            if book:
                book.available_copies += 1

            # Create fine record if applicable
            if fine_amount > 0:
                fine = Fine(
                    user_id=circulation.user_id,
                    circulation_id=circulation.id,
                    amount=fine_amount,
                    reason=f'Overdue fine for book: {book.title}',
                    status='pending',
                    created_by=current_user_id
                )
                db.session.add(fine)

            returned_books.append({
                'circulation_id': circulation.id,
                'book_title': book.title if book else 'Unknown',
                'fine_amount': fine_amount
            })
            total_fine += fine_amount

        db.session.commit()

        return jsonify({
            'message': f'Successfully returned {len(returned_books)} books',
            'returned_books': returned_books,
            'total_fine': total_fine
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Book Search for Issue
@app.route('/api/admin/books/search', methods=['GET'])
@jwt_required()
def search_books_for_issue():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        search = request.args.get('search', '')
        if len(search) < 2:
            return jsonify({'books': []}), 200

        books = Book.query.filter(
            db.or_(
                Book.title.contains(search),
                Book.author.contains(search),
                Book.access_no.contains(search)
            ),
            Book.available_copies > 0
        ).limit(10).all()

        return jsonify({
            'books': [{
                'id': book.id,
                'access_no': book.access_no,
                'title': book.title,
                'author': book.author,
                'available_copies': book.available_copies
            } for book in books]
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Get next access number for books
@app.route('/api/admin/books/next-access-number', methods=['GET'])
@jwt_required()
def get_next_access_number():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        # Get the highest access number
        last_book = Book.query.order_by(Book.id.desc()).first()

        if not last_book or not last_book.access_no:
            next_number = "1"
        else:
            # Try to extract number from access_no
            import re
            numbers = re.findall(r'\d+', last_book.access_no)
            if numbers:
                last_number = int(numbers[-1])
                next_number = str(last_number + 1)
            else:
                next_number = "1"

        return jsonify({'next_access_number': next_number}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Renew Book
@app.route('/api/admin/circulation/renew', methods=['POST'])
@jwt_required()
def renew_book():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        data = request.get_json()
        circulation_ids = data.get('circulation_ids', [])
        renewal_days = data.get('renewal_days', 14)  # Default 14 days

        if not circulation_ids:
            return jsonify({'error': 'No circulation IDs provided'}), 400

        renewed_books = []

        for circulation_id in circulation_ids:
            circulation = Circulation.query.get(circulation_id)
            if not circulation:
                continue

            if circulation.status != 'issued':
                continue

            # Check if user has outstanding fines
            user_fines = db.session.query(db.func.sum(Fine.amount)).filter(
                Fine.user_id == circulation.user_id,
                Fine.status == 'pending'
            ).scalar() or 0

            if user_fines > 0:
                continue  # Skip renewal if user has outstanding fines

            # Check renewal count (you might want to add a renewal_count field to Circulation model)
            # For now, we'll allow unlimited renewals

            # Extend due date
            from datetime import timedelta
            circulation.due_date = circulation.due_date + timedelta(days=renewal_days)

            # Get book info
            book = Book.query.get(circulation.book_id)

            renewed_books.append({
                'circulation_id': circulation.id,
                'book_title': book.title if book else 'Unknown',
                'new_due_date': circulation.due_date.isoformat(),
                'renewal_days': renewal_days
            })

        db.session.commit()

        return jsonify({
            'message': f'Successfully renewed {len(renewed_books)} books',
            'renewed_books': renewed_books
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Get next access number for ebooks
@app.route('/api/admin/ebooks/next-access-number', methods=['GET'])
@jwt_required()
def get_next_ebook_access_number():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        # Get the highest access number
        last_ebook = Ebook.query.order_by(Ebook.id.desc()).first()

        if not last_ebook or not last_ebook.access_no:
            next_number = "E1"
        else:
            # Try to extract number from access_no
            import re
            numbers = re.findall(r'\d+', last_ebook.access_no)
            if numbers:
                last_number = int(numbers[-1])
                next_number = f"E{last_number + 1}"
            else:
                next_number = "E1"

        return jsonify({'next_access_number': next_number}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Fine Management Routes

# Get all fines
@app.route('/api/admin/fines', methods=['GET'])
@jwt_required()
def get_fines():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        status = request.args.get('status', 'all')  # all, pending, paid
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))

        query = db.session.query(Fine, User).join(User, Fine.user_id == User.id)

        if status != 'all':
            query = query.filter(Fine.status == status)

        fines = query.order_by(Fine.created_date.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        return jsonify({
            'fines': [{
                'id': fine.id,
                'user_id': user.user_id,
                'user_name': user.name,
                'amount': fine.amount,
                'reason': fine.reason,
                'status': fine.status,
                'created_date': fine.created_date.isoformat(),
                'paid_date': fine.paid_date.isoformat() if fine.paid_date else None
            } for fine, user in fines.items],
            'pagination': {
                'page': fines.page,
                'pages': fines.pages,
                'per_page': fines.per_page,
                'total': fines.total
            }
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Add manual fine
@app.route('/api/admin/fines', methods=['POST'])
@jwt_required()
def add_fine():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        data = request.get_json()
        user_id = data.get('user_id')  # Roll number
        amount = data.get('amount')
        reason = data.get('reason')

        if not all([user_id, amount, reason]):
            return jsonify({'error': 'User ID, amount, and reason are required'}), 400

        # Find user
        user = User.query.filter_by(user_id=user_id).first()
        if not user:
            return jsonify({'error': 'User not found'}), 404

        fine = Fine(
            user_id=user.id,
            amount=float(amount),
            reason=reason,
            status='pending',
            created_by=current_user_id
        )

        db.session.add(fine)
        db.session.commit()

        return jsonify({
            'message': 'Fine added successfully',
            'fine': {
                'id': fine.id,
                'user_name': user.name,
                'amount': fine.amount,
                'reason': fine.reason
            }
        }), 201

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Mark fine as paid
@app.route('/api/admin/fines/<int:fine_id>/pay', methods=['POST'])
@jwt_required()
def pay_fine(fine_id):
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        fine = Fine.query.get(fine_id)
        if not fine:
            return jsonify({'error': 'Fine not found'}), 404

        if fine.status == 'paid':
            return jsonify({'error': 'Fine already paid'}), 400

        fine.status = 'paid'
        fine.paid_date = datetime.utcnow()
        db.session.commit()

        return jsonify({
            'message': 'Fine marked as paid successfully',
            'fine': {
                'id': fine.id,
                'amount': fine.amount,
                'paid_date': fine.paid_date.isoformat()
            }
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Get user's fines (for student dashboard)
@app.route('/api/student/fines', methods=['GET'])
@jwt_required()
def get_user_fines():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        fines = Fine.query.filter_by(user_id=user_id).order_by(Fine.created_date.desc()).all()

        total_pending = sum(fine.amount for fine in fines if fine.status == 'pending')

        return jsonify({
            'fines': [{
                'id': fine.id,
                'amount': fine.amount,
                'reason': fine.reason,
                'status': fine.status,
                'created_date': fine.created_date.isoformat(),
                'paid_date': fine.paid_date.isoformat() if fine.paid_date else None
            } for fine in fines],
            'total_pending': total_pending
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Get fines for a specific user (for payment management)
@app.route('/api/admin/fines/user/<user_id>', methods=['GET'])
@jwt_required()
def get_user_fines_by_id(user_id):
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        # Find user by user_id (roll number) or database id
        user = User.query.filter_by(user_id=user_id).first()
        if not user:
            user = User.query.get(user_id)

        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Get all fines for this user
        fines = Fine.query.filter_by(user_id=user.id).order_by(Fine.created_date.desc()).all()

        # Calculate total pending fines
        total_pending = sum(fine.amount for fine in fines if fine.status == 'pending')

        return jsonify({
            'user': {
                'id': user.id,
                'user_id': user.user_id,
                'name': user.name,
                'email': user.email,
                'college': user.college.name if user.college else None,
                'department': user.department.name if user.department else None
            },
            'fines': [{
                'id': fine.id,
                'amount': fine.amount,
                'reason': fine.reason,
                'status': fine.status,
                'created_date': fine.created_date.isoformat(),
                'paid_date': fine.paid_date.isoformat() if fine.paid_date else None
            } for fine in fines],
            'total_pending': total_pending
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Settings Management Routes

# Get system settings
@app.route('/api/admin/settings', methods=['GET'])
@jwt_required()
def get_settings():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin']:
            return jsonify({'error': 'Admin access required'}), 403

        # Default settings (in a real app, these would be stored in database)
        default_settings = {
            'max_books_per_student': 3,
            'max_books_per_staff': 5,
            'loan_period_days': 14,
            'daily_fine_rate': 1.0,
            'max_renewal_count': 2,
            'renewal_period_days': 7,
            'overdue_grace_period': 0
        }

        return jsonify({'settings': default_settings}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Update system settings
@app.route('/api/admin/settings', methods=['POST'])
@jwt_required()
def update_settings():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin']:
            return jsonify({'error': 'Admin access required'}), 403

        data = request.get_json()
        settings = data.get('settings', {})

        # In a real application, you would save these to a database
        # For now, we'll just validate and return success
        required_settings = [
            'max_books_per_student', 'max_books_per_staff', 'loan_period_days', 'daily_fine_rate',
            'max_renewal_count', 'renewal_period_days', 'overdue_grace_period'
        ]

        for setting in required_settings:
            if setting not in settings:
                return jsonify({'error': f'Missing setting: {setting}'}), 400

        return jsonify({'message': 'Settings updated successfully'}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Gate Entry Management Routes

# Get all gate entry credentials
@app.route('/api/admin/gate-credentials', methods=['GET'])
@jwt_required()
def get_gate_credentials():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        credentials = GateEntryCredential.query.all()

        return jsonify({
            'credentials': [{
                'id': cred.id,
                'username': cred.username,
                'name': cred.name,
                'is_active': cred.is_active,
                'created_date': cred.created_date.isoformat(),
                'created_by': cred.created_by_user.name if cred.created_by_user else 'Unknown'
            } for cred in credentials]
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Create gate entry credential
@app.route('/api/admin/gate-credentials', methods=['POST'])
@jwt_required()
def create_gate_credential():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        name = data.get('name')

        if not all([username, password, name]):
            return jsonify({'error': 'Username, password, and name are required'}), 400

        # Check if username already exists
        existing = GateEntryCredential.query.filter_by(username=username).first()
        if existing:
            return jsonify({'error': 'Username already exists'}), 400

        credential = GateEntryCredential(
            username=username,
            name=name,
            created_by=current_user_id
        )
        credential.set_password(password)

        db.session.add(credential)
        db.session.commit()

        return jsonify({
            'message': 'Gate entry credential created successfully',
            'credential': {
                'id': credential.id,
                'username': credential.username,
                'name': credential.name
            }
        }), 201

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Update gate entry credential
@app.route('/api/admin/gate-credentials/<int:credential_id>', methods=['PUT'])
@jwt_required()
def update_gate_credential(credential_id):
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        credential = GateEntryCredential.query.get(credential_id)
        if not credential:
            return jsonify({'error': 'Credential not found'}), 404

        data = request.get_json()

        if 'name' in data:
            credential.name = data['name']

        if 'is_active' in data:
            credential.is_active = data['is_active']

        if 'password' in data and data['password']:
            credential.set_password(data['password'])

        db.session.commit()

        return jsonify({'message': 'Credential updated successfully'}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Delete gate entry credential
@app.route('/api/admin/gate-credentials/<int:credential_id>', methods=['DELETE'])
@jwt_required()
def delete_gate_credential(credential_id):
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        credential = GateEntryCredential.query.get(credential_id)
        if not credential:
            return jsonify({'error': 'Credential not found'}), 404

        db.session.delete(credential)
        db.session.commit()

        return jsonify({'message': 'Credential deleted successfully'}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Gate Entry Dashboard Routes

# Gate entry login
@app.route('/api/gate/login', methods=['POST'])
def gate_login():
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')

        if not username or not password:
            return jsonify({'error': 'Username and password are required'}), 400

        credential = GateEntryCredential.query.filter_by(username=username, is_active=True).first()

        if not credential or not credential.check_password(password):
            return jsonify({'error': 'Invalid credentials'}), 401

        # Create JWT token for gate entry
        access_token = create_access_token(identity=str(credential.id), additional_claims={'type': 'gate'})

        return jsonify({
            'access_token': access_token,
            'credential': {
                'id': credential.id,
                'username': credential.username,
                'name': credential.name
            }
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Process barcode scan
@app.route('/api/gate/scan', methods=['POST'])
@jwt_required()
def process_barcode_scan():
    try:
        # Verify this is a gate entry token
        claims = get_jwt()
        if claims.get('type') != 'gate':
            return jsonify({'error': 'Invalid token type'}), 403

        gate_credential_id = int(get_jwt_identity())
        gate_credential = GateEntryCredential.query.get(gate_credential_id)
        if not gate_credential or not gate_credential.is_active:
            return jsonify({'error': 'Invalid gate credential'}), 403

        data = request.get_json()
        barcode = data.get('barcode', '').strip()

        if not barcode:
            return jsonify({'error': 'Barcode is required'}), 400

        # Find user by user_id (barcode should match user_id)
        user = User.query.filter_by(user_id=barcode).first()
        if not user:
            return jsonify({'error': 'User not found. Invalid barcode.'}), 404

        # Check if user has an active entry (last log with status 'in' and no exit_time)
        last_log = GateEntryLog.query.filter_by(user_id=user.id).order_by(GateEntryLog.created_date.desc()).first()

        current_time = datetime.utcnow()

        if last_log and last_log.status == 'in' and not last_log.exit_time:
            # User is exiting - record exit time
            last_log.exit_time = current_time
            last_log.status = 'out'
            action = 'exit'
            message = f'Exit recorded for {user.name}'
        else:
            # User is entering - create new entry log
            new_log = GateEntryLog(
                user_id=user.id,
                entry_time=current_time,
                status='in',
                scanned_by=gate_credential_id
            )
            db.session.add(new_log)
            action = 'entry'
            message = f'Entry recorded for {user.name}'

        db.session.commit()

        return jsonify({
            'success': True,
            'action': action,
            'message': message,
            'user': {
                'id': user.id,
                'user_id': user.user_id,
                'name': user.name,
                'college': user.college.name if user.college else None,
                'department': user.department.name if user.department else None
            },
            'timestamp': current_time.isoformat()
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Get gate entry logs
@app.route('/api/admin/gate-logs', methods=['GET'])
@jwt_required()
def get_gate_logs():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        date_filter = request.args.get('date')  # YYYY-MM-DD format

        query = db.session.query(GateEntryLog, User).join(User, GateEntryLog.user_id == User.id)

        if date_filter:
            from datetime import datetime
            filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
            query = query.filter(db.func.date(GateEntryLog.created_date) == filter_date)

        logs = query.order_by(GateEntryLog.created_date.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        return jsonify({
            'logs': [{
                'id': log.id,
                'user_id': user.user_id,
                'user_name': user.name,
                'college': user.college.name if user.college else None,
                'department': user.department.name if user.department else None,
                'entry_time': log.entry_time.isoformat() if log.entry_time else None,
                'exit_time': log.exit_time.isoformat() if log.exit_time else None,
                'status': log.status,
                'created_date': log.created_date.isoformat()
            } for log, user in logs.items],
            'pagination': {
                'page': logs.page,
                'pages': logs.pages,
                'per_page': logs.per_page,
                'total': logs.total
            }
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Report Generation Helper Functions
def generate_excel_report(data, filename):
    """Generate Excel report from data"""
    try:
        import pandas as pd
        import io
        from flask import send_file

        df = pd.DataFrame(data)

        # Create Excel file in memory
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Report', index=False)

        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'{filename}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        )
    except Exception as e:
        return jsonify({'error': f'Failed to generate Excel report: {str(e)}'}), 500

def generate_pdf_report(data, title, columns):
    """Generate PDF report from data"""
    try:
        from reportlab.lib import colors
        from reportlab.lib.pagesizes import letter, A4
        from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        import io
        from flask import send_file

        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4)
        elements = []

        # Styles
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=16,
            spaceAfter=30,
            alignment=1  # Center alignment
        )

        # Add title
        elements.append(Paragraph(title, title_style))
        elements.append(Spacer(1, 12))

        # Prepare table data
        table_data = [columns]  # Header row
        for row in data:
            table_data.append([str(row.get(col.lower().replace(' ', '_'), '')) for col in columns])

        # Create table
        table = Table(table_data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        elements.append(table)
        doc.build(elements)

        buffer.seek(0)

        return send_file(
            buffer,
            mimetype='application/pdf',
            as_attachment=True,
            download_name=f'{title.replace(" ", "_")}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pdf'
        )
    except Exception as e:
        return jsonify({'error': f'Failed to generate PDF report: {str(e)}'}), 500

# Reporting System Routes

# Fine Reports
@app.route('/api/admin/reports/fines', methods=['GET'])
@jwt_required()
def get_fine_reports():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        # Get query parameters
        status_filter = request.args.get('status', 'all')  # all, paid, unpaid
        from_date = request.args.get('from_date')
        to_date = request.args.get('to_date')
        college_id = request.args.get('college_id')
        department_id = request.args.get('department_id')
        export_format = request.args.get('format')  # pdf, excel

        # Build query
        query = db.session.query(Fine, User, College, Department).join(
            User, Fine.user_id == User.id
        ).outerjoin(
            College, User.college_id == College.id
        ).outerjoin(
            Department, User.department_id == Department.id
        )

        # Apply filters
        if status_filter == 'paid':
            query = query.filter(Fine.status == 'paid')
        elif status_filter == 'unpaid':
            query = query.filter(Fine.status == 'pending')

        if from_date:
            from datetime import datetime
            from_date_obj = datetime.strptime(from_date, '%Y-%m-%d')
            query = query.filter(Fine.created_date >= from_date_obj)

        if to_date:
            from datetime import datetime
            to_date_obj = datetime.strptime(to_date, '%Y-%m-%d')
            # Add one day to include the entire to_date
            to_date_obj = to_date_obj.replace(hour=23, minute=59, second=59)
            query = query.filter(Fine.created_date <= to_date_obj)

        if college_id and college_id != 'all':
            query = query.filter(User.college_id == int(college_id))

        if department_id and department_id != 'all':
            query = query.filter(User.department_id == int(department_id))

        results = query.order_by(Fine.created_date.desc()).all()

        # Format data
        report_data = []
        for fine, user, college, department in results:
            report_data.append({
                'student_id': user.user_id,
                'name': user.name,
                'college': college.name if college else 'N/A',
                'department': department.name if department else 'N/A',
                'fine_amount': fine.amount,
                'reason': fine.reason,
                'status': fine.status,
                'created_date': fine.created_date.strftime('%Y-%m-%d %H:%M:%S'),
                'paid_date': fine.paid_date.strftime('%Y-%m-%d %H:%M:%S') if fine.paid_date else 'N/A'
            })

        # Handle export formats
        if export_format == 'excel':
            return generate_excel_report(report_data, 'Fine_Report')
        elif export_format == 'pdf':
            return generate_pdf_report(report_data, 'Fine Report', [
                'Student ID', 'Name', 'College', 'Department', 'Fine Amount',
                'Reason', 'Status', 'Created Date', 'Paid Date'
            ])

        return jsonify({
            'data': report_data,
            'total': len(report_data)
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Counter Reports
@app.route('/api/admin/reports/counter', methods=['GET'])
@jwt_required()
def get_counter_reports():
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        # Get query parameters
        report_type = request.args.get('type', 'issue')  # issue, return
        from_date = request.args.get('from_date')
        to_date = request.args.get('to_date')
        college_id = request.args.get('college_id')
        department_id = request.args.get('department_id')
        export_format = request.args.get('format')  # pdf, excel

        # Build query
        query = db.session.query(Circulation, User, Book, College, Department).join(
            User, Circulation.user_id == User.id
        ).join(
            Book, Circulation.book_id == Book.id
        ).outerjoin(
            College, User.college_id == College.id
        ).outerjoin(
            Department, User.department_id == Department.id
        )

        # Apply report type filter
        if report_type == 'return':
            query = query.filter(Circulation.status == 'returned')
        else:  # issue
            query = query.filter(Circulation.status.in_(['issued', 'returned']))

        # Apply date filters
        if from_date:
            from datetime import datetime
            from_date_obj = datetime.strptime(from_date, '%Y-%m-%d')
            if report_type == 'return':
                query = query.filter(Circulation.return_date >= from_date_obj)
            else:
                query = query.filter(Circulation.issue_date >= from_date_obj)

        if to_date:
            from datetime import datetime
            to_date_obj = datetime.strptime(to_date, '%Y-%m-%d')
            to_date_obj = to_date_obj.replace(hour=23, minute=59, second=59)
            if report_type == 'return':
                query = query.filter(Circulation.return_date <= to_date_obj)
            else:
                query = query.filter(Circulation.issue_date <= to_date_obj)

        if college_id and college_id != 'all':
            query = query.filter(User.college_id == int(college_id))

        if department_id and department_id != 'all':
            query = query.filter(User.department_id == int(department_id))

        results = query.order_by(Circulation.issue_date.desc()).all()

        # Format data
        report_data = []
        for circulation, user, book, college, department in results:
            if report_type == 'return':
                report_data.append({
                    'student_id': user.user_id,
                    'name': user.name,
                    'college': college.name if college else 'N/A',
                    'department': department.name if department else 'N/A',
                    'book_title': book.title,
                    'author': book.author,
                    'access_no': book.access_no,
                    'issue_date': circulation.issue_date.strftime('%Y-%m-%d %H:%M:%S'),
                    'due_date': circulation.due_date.strftime('%Y-%m-%d %H:%M:%S'),
                    'return_date': circulation.return_date.strftime('%Y-%m-%d %H:%M:%S') if circulation.return_date else 'N/A',
                    'fine_amount': circulation.fine_amount or 0
                })
            else:  # issue
                report_data.append({
                    'student_id': user.user_id,
                    'name': user.name,
                    'college': college.name if college else 'N/A',
                    'department': department.name if department else 'N/A',
                    'book_title': book.title,
                    'author': book.author,
                    'access_no': book.access_no,
                    'issue_date': circulation.issue_date.strftime('%Y-%m-%d %H:%M:%S'),
                    'due_date': circulation.due_date.strftime('%Y-%m-%d %H:%M:%S'),
                    'status': circulation.status
                })

        # Handle export formats
        if export_format == 'excel':
            return generate_excel_report(report_data, f'{report_type.title()}_Report')
        elif export_format == 'pdf':
            if report_type == 'return':
                columns = ['Student ID', 'Name', 'College', 'Department', 'Book Title',
                          'Author', 'Access No', 'Issue Date', 'Due Date', 'Return Date', 'Fine Amount']
            else:
                columns = ['Student ID', 'Name', 'College', 'Department', 'Book Title',
                          'Author', 'Access No', 'Issue Date', 'Due Date', 'Status']
            return generate_pdf_report(report_data, f'{report_type.title()} Report', columns)

        return jsonify({
            'data': report_data,
            'total': len(report_data)
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/student/dashboard', methods=['GET'])
@jwt_required()
def get_student_dashboard():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)

        if not user:
            return jsonify({'error': 'User not found'}), 404

        return jsonify({
            'user': {
                'name': user.name,
                'user_id': user.user_id,
                'email': user.email,
                'college': user.college.name if user.college else None,
                'department': user.department.name if user.department else None,
                'validity_date': user.validity_date.isoformat()
            },
            'stats': {
                'books_borrowed': 0,
                'total_books_read': 0,
                'total_fines': 0
            },
            'borrowed_books': []
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/students/sample', methods=['GET'])
@jwt_required()
def download_students_sample():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        import pandas as pd
        import io
        from flask import send_file
        from datetime import date, timedelta

        # Create sample data
        sample_data = {
            'user_id': ['CS2024001', 'CS2024002', 'IT2024001'],
            'name': ['John Doe', 'Jane Smith', 'Robert Johnson'],
            'email': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
            'dob': ['2000-01-15', '1999-12-20', '2001-03-10'],
            'validity_date': [(date.today() + timedelta(days=365)).isoformat(),
                             (date.today() + timedelta(days=365)).isoformat(),
                             (date.today() + timedelta(days=365)).isoformat()]
        }

        df = pd.DataFrame(sample_data)

        # Create Excel file in memory
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Students Sample', index=False)

        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name='students_sample.xlsx'
        )

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Ebook Management Routes
@app.route('/api/admin/ebooks', methods=['GET'])
@jwt_required()
def get_ebooks():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        search = request.args.get('search', '')

        query = Ebook.query
        if search:
            query = query.filter(
                Ebook.title.contains(search) |
                Ebook.author.contains(search) |
                Ebook.access_no.contains(search)
            )

        ebooks = query.paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            'ebooks': [{
                'id': ebook.id,
                'access_no': ebook.access_no,
                'title': ebook.title,
                'author': ebook.author,
                'publisher': ebook.publisher,
                'department': ebook.department,
                'category': ebook.category,
                'file_format': ebook.file_format,
                'file_size': ebook.file_size,
                'download_count': ebook.download_count,
                'created_at': ebook.created_at.isoformat()
            } for ebook in ebooks.items],
            'pagination': {
                'page': ebooks.page,
                'pages': ebooks.pages,
                'per_page': ebooks.per_page,
                'total': ebooks.total
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/ebooks', methods=['POST'])
@jwt_required()
def create_ebook():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        data = request.get_json()

        # Check if access number already exists
        existing = Ebook.query.filter_by(access_no=data.get('access_no')).first()
        if existing:
            return jsonify({'error': 'Access number already exists'}), 400

        ebook = Ebook(
            access_no=data.get('access_no'),
            title=data.get('title'),
            author=data.get('author'),
            publisher=data.get('publisher'),
            department=data.get('department'),
            category=data.get('category'),
            file_format=data.get('file_format'),
            file_size=data.get('file_size'),
            file_path=data.get('file_path', '')
        )

        db.session.add(ebook)
        db.session.commit()

        return jsonify({
            'message': 'Ebook created successfully',
            'ebook': {
                'id': ebook.id,
                'access_no': ebook.access_no,
                'title': ebook.title,
                'author': ebook.author
            }
        }), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/ebooks/sample', methods=['GET'])
@jwt_required()
def download_ebooks_sample():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        import pandas as pd
        import io
        from flask import send_file

        # Create sample data
        sample_data = {
            'access_no': ['E001', 'E002', 'E003'],
            'title': ['Digital Signal Processing', 'Machine Learning Basics', 'Web Development Guide'],
            'author': ['Dr. Smith', 'Prof. Johnson', 'Tech Team'],
            'publisher': ['Tech Publications', 'Academic Press', 'Online Books'],
            'department': ['Electronics', 'Computer Science', 'Information Technology'],
            'category': ['Reference', 'Textbook', 'Guide'],
            'file_format': ['PDF', 'EPUB', 'PDF'],
            'file_size': ['15MB', '8MB', '12MB']
        }

        df = pd.DataFrame(sample_data)

        # Create Excel file in memory
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Ebooks Sample', index=False)

        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name='ebooks_sample.xlsx'
        )

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/ebooks/bulk', methods=['POST'])
@jwt_required()
def bulk_create_ebooks():
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        if 'file' not in request.files:
            return jsonify({'error': 'No file uploaded'}), 400

        file = request.files['file']

        # Read Excel file
        import pandas as pd
        df = pd.read_excel(file)

        # Validate required columns
        required_columns = ['access_no', 'title', 'author', 'publisher', 'department', 'category', 'file_format', 'file_size']
        if not all(col in df.columns for col in required_columns):
            return jsonify({'error': f'Excel file must contain columns: {required_columns}'}), 400

        created_ebooks = []
        errors = []

        for index, row in df.iterrows():
            try:
                access_no = str(row['access_no'])

                # Check if ebook already exists
                existing = Ebook.query.filter_by(access_no=access_no).first()
                if existing:
                    errors.append(f"Row {index + 1}: Access number {access_no} already exists")
                    continue

                ebook = Ebook(
                    access_no=access_no,
                    title=row['title'],
                    author=row['author'],
                    publisher=row['publisher'],
                    department=row['department'],
                    category=row['category'],
                    file_format=row['file_format'],
                    file_size=row['file_size']
                )

                db.session.add(ebook)
                created_ebooks.append({
                    'access_no': access_no,
                    'title': row['title'],
                    'author': row['author']
                })

            except Exception as e:
                errors.append(f"Row {index + 1}: {str(e)}")

        db.session.commit()

        return jsonify({
            'message': f'Successfully created {len(created_ebooks)} e-books',
            'created_ebooks': created_ebooks,
            'errors': errors
        }), 201

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Update Ebook
@app.route('/api/admin/ebooks/<int:ebook_id>', methods=['PUT'])
@jwt_required()
def update_ebook(ebook_id):
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        ebook = Ebook.query.get(ebook_id)
        if not ebook:
            return jsonify({'error': 'E-book not found'}), 404

        data = request.get_json()
        title = data.get('title')
        author = data.get('author')
        publisher = data.get('publisher')
        category = data.get('category')
        file_format = data.get('file_format')
        file_size = data.get('file_size')

        if not all([title, author]):
            return jsonify({'error': 'Title and author are required'}), 400

        ebook.title = title
        ebook.author = author
        ebook.publisher = publisher
        ebook.category = category
        ebook.format = file_format
        ebook.file_size = file_size

        db.session.commit()

        return jsonify({
            'message': 'E-book updated successfully',
            'ebook': {
                'id': ebook.id,
                'title': ebook.title,
                'author': ebook.author
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Delete Ebook
@app.route('/api/admin/ebooks/<int:ebook_id>', methods=['DELETE'])
@jwt_required()
def delete_ebook(ebook_id):
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Admin/Librarian access required'}), 403

        ebook = Ebook.query.get(ebook_id)
        if not ebook:
            return jsonify({'error': 'E-book not found'}), 404

        db.session.delete(ebook)
        db.session.commit()

        return jsonify({'message': 'E-book deleted successfully'}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Update User
@app.route('/api/admin/users/<int:user_id>', methods=['PUT'])
@jwt_required()
def update_user(user_id):
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        data = request.get_json()
        name = data.get('name')
        email = data.get('email')
        college_id = data.get('college_id')
        department_id = data.get('department_id')
        validity_date = data.get('validity_date')
        is_active = data.get('is_active', True)

        if not all([name, email]):
            return jsonify({'error': 'Name and email are required'}), 400

        # Check if another user with same email exists
        existing_email = User.query.filter(User.email == email, User.id != user_id).first()
        if existing_email:
            return jsonify({'error': 'Email already exists'}), 400

        user.name = name
        user.email = email
        user.college_id = college_id
        user.department_id = department_id
        user.is_active = is_active

        if validity_date:
            user.validity_date = datetime.strptime(validity_date, '%Y-%m-%d').date()

        db.session.commit()

        return jsonify({
            'message': 'User updated successfully',
            'user': {
                'id': user.id,
                'name': user.name,
                'email': user.email
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Delete User
@app.route('/api/admin/users/<int:user_id>', methods=['DELETE'])
@jwt_required()
def delete_user(user_id):
    try:
        current_user_id = int(get_jwt_identity())
        current_user = User.query.get(current_user_id)
        if not current_user or current_user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Check if user has active circulations
        active_circulations = Circulation.query.filter_by(user_id=user_id, status='issued').count()
        if active_circulations > 0:
            return jsonify({'error': 'Cannot delete user with active book loans'}), 400

        db.session.delete(user)
        db.session.commit()

        return jsonify({'message': 'User deleted successfully'}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/')
def index():
    return {'message': 'Library Management System API'}

def run_migrations():
    """Run database migrations to add missing columns"""
    try:
        print("Checking database schema...")

        # Check if fine_amount column exists in circulations table
        from sqlalchemy import inspect
        inspector = inspect(db.engine)

        # Check if circulations table exists and add fine_amount column
        if 'circulations' in inspector.get_table_names():
            columns = [col['name'] for col in inspector.get_columns('circulations')]

            if 'fine_amount' not in columns:
                print("Adding fine_amount column to circulations table...")
                with db.engine.connect() as conn:
                    conn.execute(db.text("ALTER TABLE circulations ADD COLUMN fine_amount REAL DEFAULT 0.0"))
                    conn.commit()
                print("✅ fine_amount column added successfully!")

        # Check if users table exists and add user_role column
        if 'users' in inspector.get_table_names():
            columns = [col['name'] for col in inspector.get_columns('users')]

            if 'user_role' not in columns:
                print("Adding user_role column to users table...")
                with db.engine.connect() as conn:
                    conn.execute(db.text("ALTER TABLE users ADD COLUMN user_role VARCHAR(20) DEFAULT 'student'"))
                    conn.commit()
                print("✅ user_role column added successfully!")

        # Check if gate entry tables exist
        table_names = inspector.get_table_names()

        if 'gate_entry_credentials' not in table_names:
            print("Creating gate_entry_credentials table...")
            # Table will be created by db.create_all()

        if 'gate_entry_logs' not in table_names:
            print("Creating gate_entry_logs table...")
            # Table will be created by db.create_all()

        print("✅ Database schema is up to date!")

    except Exception as e:
        print(f"❌ Migration error: {e}")
        print("🔧 If this error persists, you may need to reset the database.")
        print("   To reset: Run 'python reset_database.py' or delete 'library.db' and restart.")

if __name__ == '__main__':
    with app.app_context():
        # Create all tables
        db.create_all()

        # Run migrations for existing databases
        run_migrations()

        # Create default admin user if not exists
        try:
            admin_user = User.query.filter_by(email='<EMAIL>').first()
            if not admin_user:
                print("Creating default admin user...")
                admin_user = User(
                    user_id='ADMIN001',
                    name='System Administrator',
                    email='<EMAIL>',
                    role='admin'
                )
                admin_user.set_password('admin123')
                db.session.add(admin_user)
                db.session.commit()
                print("✅ Default admin user created!")
                print("   Email: <EMAIL>")
                print("   Password: admin123")
        except Exception as e:
            print(f"Admin user creation error: {e}")

    print("🚀 Starting Library Management System...")
    app.run(debug=True)
