import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import StudentHome from './student/StudentHome'
import BrowseBooks from './student/BrowseBooks'
import BrowseEbooks from './student/BrowseEbooks'
import BorrowingHistory from './student/BorrowingHistory'
import { useAuth } from '../contexts/AuthContext'

const StudentDashboard = () => {
  const { user } = useAuth()

  return (
    <div className="student-dashboard">
      <div className="student-header">
        <div className="student-info">
          <h1>Welcome, {user?.name}</h1>
          <p>Student ID: {user?.user_id}</p>
        </div>
        <div className="student-nav">
          <a href="/student">Dashboard</a>
          <a href="/student/books">Browse Books</a>
          <a href="/student/ebooks">Browse E-books</a>
          <a href="/student/history">My History</a>
          <button onClick={() => window.location.href = '/login'}>Logout</button>
        </div>
      </div>
      
      <div className="student-content">
        <Routes>
          <Route path="/" element={<StudentHome />} />
          <Route path="/books" element={<BrowseBooks />} />
          <Route path="/ebooks" element={<BrowseEbooks />} />
          <Route path="/history" element={<BorrowingHistory />} />
          <Route path="*" element={<Navigate to="/student" />} />
        </Routes>
      </div>
    </div>
  )
}

export default StudentDashboard
