import React, { useState, useEffect, useRef } from 'react'
import { <PERSON>an, User, Clock, AlertCircle, CheckCircle, LogOut, Wifi, WifiOff } from 'lucide-react'
import axios from 'axios'

const GateEntryDashboard = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [credential, setCredential] = useState(null)
  const [barcode, setBarcode] = useState('')
  const [lastScanResult, setLastScanResult] = useState(null)
  const [isScanning, setIsScanning] = useState(false)
  const [scannerConnected, setScannerConnected] = useState(true)
  const [recentScans, setRecentScans] = useState([])
  const [loginForm, setLoginForm] = useState({ username: '', password: '' })
  const [loginError, setLoginError] = useState('')

  const barcodeInputRef = useRef(null)
  const scanTimeoutRef = useRef(null)

  useEffect(() => {
    // Check if already logged in
    const token = localStorage.getItem('gateToken')
    const savedCredential = localStorage.getItem('gateCredential')

    if (token && savedCredential) {
      try {
        setIsLoggedIn(true)
        setCredential(JSON.parse(savedCredential))
        // Set up axios defaults for gate entry
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
      } catch (error) {
        console.error('Error parsing saved credential:', error)
        // Clear invalid data
        localStorage.removeItem('gateToken')
        localStorage.removeItem('gateCredential')
      }
    }

    // Simulate scanner connection check
    checkScannerConnection()
  }, [])

  useEffect(() => {
    // Focus on barcode input when logged in
    if (isLoggedIn && barcodeInputRef.current) {
      barcodeInputRef.current.focus()
    }
  }, [isLoggedIn])

  const checkScannerConnection = () => {
    // In a real implementation, this would check for actual barcode scanner hardware
    // For demo purposes, we'll assume scanner is connected
    setScannerConnected(true)
  }

  const handleLogin = async (e) => {
    e.preventDefault()
    setLoginError('')

    if (!loginForm.username || !loginForm.password) {
      setLoginError('Please enter both username and password')
      return
    }

    try {
      console.log('Attempting gate login with:', loginForm)

      // Create a new axios instance to avoid conflicts with main app auth
      const gateAxios = axios.create({
        baseURL: window.location.origin,
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await gateAxios.post('/api/gate/login', loginForm)
      console.log('Gate login response:', response.data)

      const { access_token, credential } = response.data

      if (!access_token || !credential) {
        throw new Error('Invalid response from server')
      }

      localStorage.setItem('gateToken', access_token)
      localStorage.setItem('gateCredential', JSON.stringify(credential))

      // Set up axios defaults for gate entry
      axios.defaults.headers.common['Authorization'] = `Bearer ${access_token}`

      setIsLoggedIn(true)
      setCredential(credential)
      setLoginForm({ username: '', password: '' })
      console.log('Gate login successful')
    } catch (error) {
      console.error('Gate login error:', error)
      const errorMessage = error.response?.data?.error || error.message || 'Login failed'
      setLoginError(errorMessage)
    }
  }

  const createTestCredential = async () => {
    try {
      const response = await axios.post('/api/gate/create-default')
      alert('Test credential created!\nUsername: gate_operator\nPassword: password123')
      console.log('Test credential created:', response.data)
    } catch (error) {
      console.error('Failed to create test credential:', error)
      alert('Failed to create test credential: ' + (error.response?.data?.error || error.message))
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('gateToken')
    localStorage.removeItem('gateCredential')
    delete axios.defaults.headers.common['Authorization']
    
    setIsLoggedIn(false)
    setCredential(null)
    setRecentScans([])
    setLastScanResult(null)
  }

  const handleBarcodeInput = (e) => {
    const value = e.target.value
    setBarcode(value)

    // Clear previous timeout
    if (scanTimeoutRef.current) {
      clearTimeout(scanTimeoutRef.current)
    }

    // Auto-submit after 100ms of no input (simulates barcode scanner behavior)
    if (value.length > 0) {
      scanTimeoutRef.current = setTimeout(() => {
        processScan(value)
      }, 100)
    }
  }

  const handleManualScan = () => {
    if (barcode.trim()) {
      processScan(barcode.trim())
    }
  }

  const processScan = async (scannedBarcode) => {
    if (isScanning) return

    setIsScanning(true)
    setBarcode('')

    try {
      const response = await axios.post('/api/gate/scan', {
        barcode: scannedBarcode
      })

      const result = response.data
      setLastScanResult(result)

      // Add to recent scans
      const newScan = {
        id: Date.now(),
        ...result,
        barcode: scannedBarcode
      }
      setRecentScans(prev => [newScan, ...prev.slice(0, 9)]) // Keep last 10 scans

      // Clear result after 5 seconds
      setTimeout(() => {
        setLastScanResult(null)
      }, 5000)

    } catch (error) {
      const errorResult = {
        success: false,
        message: error.response?.data?.error || 'Scan failed',
        action: 'error'
      }
      setLastScanResult(errorResult)

      setTimeout(() => {
        setLastScanResult(null)
      }, 5000)
    } finally {
      setIsScanning(false)
      // Refocus on input for next scan
      if (barcodeInputRef.current) {
        barcodeInputRef.current.focus()
      }
    }
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleManualScan()
    }
  }

  if (!isLoggedIn) {
    return (
      <div className="gate-login">
        <div className="login-container">
          <div className="login-header">
            <Scan size={48} />
            <h1>Gate Entry System</h1>
            <p>Please login with your gate entry credentials</p>
          </div>
          
          <form onSubmit={handleLogin} className="login-form">
            <div className="form-group">
              <label>Username</label>
              <input
                type="text"
                value={loginForm.username}
                onChange={(e) => setLoginForm({...loginForm, username: e.target.value})}
                placeholder="Enter username"
                required
              />
            </div>
            <div className="form-group">
              <label>Password</label>
              <input
                type="password"
                value={loginForm.password}
                onChange={(e) => setLoginForm({...loginForm, password: e.target.value})}
                placeholder="Enter password"
                required
              />
            </div>
            {loginError && (
              <div className="error-message">
                <AlertCircle size={16} />
                {loginError}
              </div>
            )}
            <button type="submit" className="btn btn-primary">
              Login
            </button>
          </form>

          <div className="test-section">
            <p style={{ fontSize: '0.9rem', color: '#666', marginTop: '1rem' }}>
              For testing: Create default credentials
            </p>
            <button
              type="button"
              onClick={createTestCredential}
              className="btn btn-secondary"
              style={{ marginTop: '0.5rem', fontSize: '0.8rem' }}
            >
              Create Test Credential
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="gate-dashboard">
      {/* Header */}
      <div className="gate-header">
        <div className="header-left">
          <Scan size={32} />
          <div>
            <h1>Gate Entry Dashboard</h1>
            <p>Operator: {credential?.name}</p>
          </div>
        </div>
        <div className="header-right">
          <div className={`scanner-status ${scannerConnected ? 'connected' : 'disconnected'}`}>
            {scannerConnected ? <Wifi size={16} /> : <WifiOff size={16} />}
            <span>{scannerConnected ? 'Scanner Connected' : 'Scanner Disconnected'}</span>
          </div>
          <button onClick={handleLogout} className="btn btn-secondary">
            <LogOut size={16} />
            Logout
          </button>
        </div>
      </div>

      {/* Scanner Error */}
      {!scannerConnected && (
        <div className="scanner-error">
          <AlertCircle size={24} />
          <div>
            <h3>Barcode Scanner Not Connected</h3>
            <p>Please check the scanner connection and try again.</p>
          </div>
        </div>
      )}

      {/* Main Scanning Area */}
      <div className="scanning-area">
        <div className="scan-input-section">
          <div className="scan-input">
            <Scan size={24} />
            <input
              ref={barcodeInputRef}
              type="text"
              value={barcode}
              onChange={handleBarcodeInput}
              onKeyPress={handleKeyPress}
              placeholder="Scan student barcode or enter manually..."
              disabled={isScanning || !scannerConnected}
              autoFocus
            />
            <button 
              onClick={handleManualScan}
              disabled={isScanning || !barcode.trim() || !scannerConnected}
              className="btn btn-primary"
            >
              {isScanning ? 'Processing...' : 'Scan'}
            </button>
          </div>
        </div>

        {/* Scan Result */}
        {lastScanResult && (
          <div className={`scan-result ${lastScanResult.success ? 'success' : 'error'}`}>
            <div className="result-icon">
              {lastScanResult.success ? <CheckCircle size={32} /> : <AlertCircle size={32} />}
            </div>
            <div className="result-content">
              <h2>{lastScanResult.message}</h2>
              {lastScanResult.user && (
                <div className="user-details">
                  <div className="user-info">
                    <User size={20} />
                    <div>
                      <p><strong>{lastScanResult.user.name}</strong></p>
                      <p>ID: {lastScanResult.user.user_id}</p>
                      {lastScanResult.user.college && (
                        <p>{lastScanResult.user.college} - {lastScanResult.user.department}</p>
                      )}
                    </div>
                  </div>
                  <div className="action-info">
                    <div className={`action-badge ${lastScanResult.action}`}>
                      {lastScanResult.action === 'entry' ? 'ENTRY' : 'EXIT'}
                    </div>
                    <div className="timestamp">
                      <Clock size={14} />
                      {new Date(lastScanResult.timestamp).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Recent Scans */}
      <div className="recent-scans">
        <h3>Recent Scans</h3>
        <div className="scans-list">
          {recentScans.length === 0 ? (
            <p className="no-scans">No recent scans</p>
          ) : (
            recentScans.map((scan) => (
              <div key={scan.id} className={`scan-item ${scan.success ? 'success' : 'error'}`}>
                <div className="scan-user">
                  {scan.user ? (
                    <>
                      <strong>{scan.user.name}</strong>
                      <span>({scan.user.user_id})</span>
                    </>
                  ) : (
                    <span>Invalid Barcode: {scan.barcode}</span>
                  )}
                </div>
                <div className="scan-action">
                  {scan.success && (
                    <span className={`action ${scan.action}`}>
                      {scan.action.toUpperCase()}
                    </span>
                  )}
                  <span className="time">
                    {new Date(scan.timestamp).toLocaleTimeString()}
                  </span>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  )
}

export default GateEntryDashboard
