import React, { useState, useEffect } from 'react'
import { DollarSign, Plus, Search, User, Calendar, CheckCircle, AlertCircle, FileText } from 'lucide-react'
import axios from 'axios'

const FineManagement = () => {
  const [fines, setFines] = useState([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const [formData, setFormData] = useState({
    userId: '',
    amount: '',
    reason: ''
  })

  const [errors, setErrors] = useState({})

  useEffect(() => {
    fetchFines()
  }, [currentPage, statusFilter])

  const fetchFines = async () => {
    try {
      const response = await axios.get('/admin/fines', {
        params: {
          status: statusFilter,
          page: currentPage,
          per_page: 10
        }
      })
      setFines(response.data.fines)
      setTotalPages(response.data.pagination.pages)
    } catch (error) {
      console.error('Failed to fetch fines:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData({
      ...formData,
      [name]: value
    })
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      })
    }
  }

  const validateForm = () => {
    const newErrors = {}

    if (!formData.userId.trim()) {
      newErrors.userId = 'User ID is required'
    }

    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      newErrors.amount = 'Valid amount is required'
    }

    if (!formData.reason.trim()) {
      newErrors.reason = 'Reason is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    try {
      await axios.post('/admin/fines', {
        user_id: formData.userId,
        amount: parseFloat(formData.amount),
        reason: formData.reason
      })

      alert('Fine added successfully!')
      setShowAddForm(false)
      setFormData({ userId: '', amount: '', reason: '' })
      setErrors({})
      fetchFines()
    } catch (error) {
      alert(error.response?.data?.error || 'Failed to add fine')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handlePayFine = async (fineId) => {
    if (window.confirm('Mark this fine as paid?')) {
      try {
        await axios.post(`/admin/fines/${fineId}/pay`)
        alert('Fine marked as paid successfully!')
        fetchFines()
      } catch (error) {
        alert(error.response?.data?.error || 'Failed to mark fine as paid')
      }
    }
  }

  const resetForm = () => {
    setFormData({ userId: '', amount: '', reason: '' })
    setErrors({})
  }

  if (loading) {
    return <div className="loading">Loading fines...</div>
  }

  return (
    <div className="fine-management">
      <div className="page-header">
        <div>
          <h1>Fine Management</h1>
          <p>Manage user fines and payments</p>
        </div>
        <div className="header-actions">
          <button 
            className="btn btn-primary"
            onClick={() => setShowAddForm(true)}
          >
            <Plus size={16} />
            Add Fine
          </button>
        </div>
      </div>

      <div className="filters">
        <div className="search-box">
          <Search size={16} />
          <input
            type="text"
            placeholder="Search by user name or ID..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="filter-group">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="all">All Fines</option>
            <option value="pending">Pending</option>
            <option value="paid">Paid</option>
          </select>
        </div>
      </div>

      <div className="fines-table">
        <table>
          <thead>
            <tr>
              <th>User</th>
              <th>Amount</th>
              <th>Reason</th>
              <th>Status</th>
              <th>Created Date</th>
              <th>Paid Date</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {fines.map((fine) => (
              <tr key={fine.id}>
                <td>
                  <div className="user-info">
                    <strong>{fine.user_name}</strong>
                    <small>ID: {fine.user_id}</small>
                  </div>
                </td>
                <td>
                  <span className="amount">₹{fine.amount.toFixed(2)}</span>
                </td>
                <td>
                  <span className="reason">{fine.reason}</span>
                </td>
                <td>
                  <span className={`status ${fine.status}`}>
                    {fine.status === 'paid' ? (
                      <>
                        <CheckCircle size={14} />
                        Paid
                      </>
                    ) : (
                      <>
                        <AlertCircle size={14} />
                        Pending
                      </>
                    )}
                  </span>
                </td>
                <td>{new Date(fine.created_date).toLocaleDateString()}</td>
                <td>
                  {fine.paid_date ? new Date(fine.paid_date).toLocaleDateString() : '-'}
                </td>
                <td>
                  <div className="actions">
                    {fine.status === 'pending' && (
                      <button
                        className="btn-icon success"
                        onClick={() => handlePayFine(fine.id)}
                        title="Mark as Paid"
                      >
                        <CheckCircle size={14} />
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="pagination">
        <button 
          disabled={currentPage === 1}
          onClick={() => setCurrentPage(currentPage - 1)}
        >
          Previous
        </button>
        <span>Page {currentPage} of {totalPages}</span>
        <button 
          disabled={currentPage === totalPages}
          onClick={() => setCurrentPage(currentPage + 1)}
        >
          Next
        </button>
      </div>

      {/* Add Fine Modal */}
      {showAddForm && (
        <div className="modal">
          <div className="modal-content">
            <div className="modal-header">
              <h2>Add Manual Fine</h2>
              <button onClick={() => {
                setShowAddForm(false)
                resetForm()
              }}>×</button>
            </div>
            <form onSubmit={handleSubmit}>
              <div className="form-grid">
                <div className="form-group">
                  <label>User ID (Roll Number) *</label>
                  <div className="input-with-icon">
                    <User size={16} />
                    <input
                      type="text"
                      name="userId"
                      value={formData.userId}
                      onChange={handleInputChange}
                      placeholder="Enter user ID"
                      className={errors.userId ? 'error' : ''}
                    />
                  </div>
                  {errors.userId && <span className="error-text">{errors.userId}</span>}
                </div>
                <div className="form-group">
                  <label>Fine Amount *</label>
                  <div className="input-with-icon">
                    <DollarSign size={16} />
                    <input
                      type="number"
                      name="amount"
                      value={formData.amount}
                      onChange={handleInputChange}
                      placeholder="0.00"
                      step="0.01"
                      min="0"
                      className={errors.amount ? 'error' : ''}
                    />
                  </div>
                  {errors.amount && <span className="error-text">{errors.amount}</span>}
                </div>
                <div className="form-group full-width">
                  <label>Reason *</label>
                  <textarea
                    name="reason"
                    value={formData.reason}
                    onChange={handleInputChange}
                    placeholder="Enter reason for fine (e.g., Lost book, Damaged book, etc.)"
                    rows="3"
                    className={errors.reason ? 'error' : ''}
                  />
                  {errors.reason && <span className="error-text">{errors.reason}</span>}
                </div>
              </div>
              <div className="modal-actions">
                <button type="button" onClick={() => {
                  setShowAddForm(false)
                  resetForm()
                }}>
                  Cancel
                </button>
                <button type="submit" className="btn btn-primary" disabled={isSubmitting}>
                  {isSubmitting ? 'Adding...' : 'Add Fine'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}

export default FineManagement
