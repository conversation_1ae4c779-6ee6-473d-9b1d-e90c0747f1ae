import React, { useState } from 'react'
import { Search, User, Book, Calendar, AlertCircle, CheckCircle, Clock, DollarSign, RotateCcw } from 'lucide-react'
import axios from 'axios'

const ReturnBook = () => {
  const [userInfo, setUserInfo] = useState(null)
  const [userLoading, setUserLoading] = useState(false)
  const [selectedBooks, setSelectedBooks] = useState([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isRenewing, setIsRenewing] = useState(false)
  const [errors, setErrors] = useState({})
  const [totalFine, setTotalFine] = useState(0)

  const [formData, setFormData] = useState({
    userId: ''
  })

  const handleUserSearch = async (userId) => {
    if (!userId.trim()) {
      setUserInfo(null)
      setSelectedBooks([])
      setTotalFine(0)
      return
    }

    setUserLoading(true)
    try {
      const response = await axios.get(`/admin/circulation/user/${userId}`)
      setUserInfo(response.data)
      setFormData(prev => ({ ...prev, userId: userId }))
      setErrors(prev => ({ ...prev, user: '' }))
      setSelectedBooks([])
      setTotalFine(0)
    } catch (error) {
      setUserInfo(null)
      setSelectedBooks([])
      setTotalFine(0)
      setErrors(prev => ({ ...prev, user: error.response?.data?.error || 'User not found' }))
    } finally {
      setUserLoading(false)
    }
  }

  const toggleBookSelection = (book) => {
    const isSelected = selectedBooks.some(b => b.circulation_id === book.circulation_id)

    if (isSelected) {
      const newSelected = selectedBooks.filter(b => b.circulation_id !== book.circulation_id)
      setSelectedBooks(newSelected)
      calculateTotalFine(newSelected)
    } else {
      const newSelected = [...selectedBooks, book]
      setSelectedBooks(newSelected)
      calculateTotalFine(newSelected)
    }
  }

  const calculateTotalFine = (books) => {
    const total = books.reduce((sum, book) => sum + (book.fine_amount || 0), 0)
    setTotalFine(total)
  }

  const selectAllBooks = () => {
    if (selectedBooks.length === userInfo.current_books.length) {
      setSelectedBooks([])
      setTotalFine(0)
    } else {
      setSelectedBooks([...userInfo.current_books])
      calculateTotalFine(userInfo.current_books)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!userInfo) {
      setErrors({ user: 'Please search and select a valid user' })
      return
    }

    if (selectedBooks.length === 0) {
      setErrors({ books: 'Please select at least one book to return' })
      return
    }

    setIsSubmitting(true)
    try {
      const circulation_ids = selectedBooks.map(book => book.circulation_id)

      const response = await axios.post('/admin/circulation/return', {
        circulation_ids: circulation_ids
      })

      alert(`Successfully returned ${response.data.returned_books.length} books. ${response.data.total_fine > 0 ? `Total fine: $${response.data.total_fine.toFixed(2)}` : 'No fines applied.'}`)

      // Refresh user info
      handleUserSearch(userInfo.user.user_id)

    } catch (error) {
      alert(error.response?.data?.error || 'Failed to return books')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleRenewal = async () => {
    if (!userInfo) {
      setErrors({ user: 'Please search and select a valid user' })
      return
    }

    if (selectedBooks.length === 0) {
      setErrors({ books: 'Please select at least one book to renew' })
      return
    }

    // Check if user has outstanding fines
    if (userInfo.total_fine > 0) {
      setErrors({ user: `Cannot renew books. User has outstanding fines of $${userInfo.total_fine}. Please clear fines first.` })
      return
    }

    // Check if any selected books are overdue
    const overdueBooks = selectedBooks.filter(book => book.is_overdue)
    if (overdueBooks.length > 0) {
      setErrors({ books: 'Cannot renew overdue books. Please return them first.' })
      return
    }

    setIsRenewing(true)
    try {
      const circulation_ids = selectedBooks.map(book => book.circulation_id)

      const response = await axios.post('/admin/circulation/renew', {
        circulation_ids: circulation_ids,
        renewal_days: 14 // Default 14 days renewal
      })

      alert(`Successfully renewed ${response.data.renewed_books.length} books for 14 more days!`)

      // Refresh user info
      handleUserSearch(userInfo.user.user_id)

    } catch (error) {
      alert(error.response?.data?.error || 'Failed to renew books')
    } finally {
      setIsRenewing(false)
    }
  }

  return (
    <div className="return-book">
      <div className="page-header">
        <h1>Return Book</h1>
        <p>Process book returns and calculate fines</p>
      </div>

      <div className="return-book-container">
        <div className="return-form-section">
          <div className="form-card">
            <h2>Book Return Form</h2>

            <form onSubmit={handleSubmit}>
              {/* User Search */}
              <div className="form-group">
                <label>Student/Staff ID *</label>
                <div className="search-input">
                  <User size={16} />
                  <input
                    type="text"
                    placeholder="Enter user ID (roll number)"
                    value={formData.userId}
                    onChange={(e) => {
                      setFormData(prev => ({ ...prev, userId: e.target.value }))
                      handleUserSearch(e.target.value)
                    }}
                    className={errors.user ? 'error' : ''}
                  />
                  {userLoading && <div className="loading-spinner">Loading...</div>}
                </div>
                {errors.user && <span className="error-text">{errors.user}</span>}
              </div>

              {/* Books Selection */}
              {userInfo && userInfo.current_books.length > 0 && (
                <div className="books-selection-section">
                  <div className="section-header">
                    <h3>Select Books to Return</h3>
                    <button
                      type="button"
                      className="btn btn-secondary"
                      onClick={selectAllBooks}
                    >
                      {selectedBooks.length === userInfo.current_books.length ? 'Deselect All' : 'Select All'}
                    </button>
                  </div>

                  {errors.books && <span className="error-text">{errors.books}</span>}

                  <div className="books-grid">
                    {userInfo.current_books.map((book) => (
                      <div
                        key={book.circulation_id}
                        className={`book-card ${selectedBooks.some(b => b.circulation_id === book.circulation_id) ? 'selected' : ''} ${book.is_overdue ? 'overdue' : ''}`}
                        onClick={() => toggleBookSelection(book)}
                      >
                        <div className="book-header">
                          <div className="book-checkbox">
                            <input
                              type="checkbox"
                              checked={selectedBooks.some(b => b.circulation_id === book.circulation_id)}
                              onChange={() => toggleBookSelection(book)}
                            />
                          </div>
                          <div className="book-status">
                            {book.is_overdue ? (
                              <div className="status-badge overdue">
                                <Clock size={12} />
                                Overdue
                              </div>
                            ) : (
                              <div className="status-badge on-time">
                                <CheckCircle size={12} />
                                On Time
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="book-details">
                          <h4>{book.title}</h4>
                          <p>by {book.author}</p>
                          <small>Access No: {book.access_no}</small>
                        </div>

                        <div className="book-dates">
                          <div className="date-row">
                            <span>Issued: {new Date(book.issue_date).toLocaleDateString()}</span>
                          </div>
                          <div className="date-row">
                            <span>Due: {new Date(book.due_date).toLocaleDateString()}</span>
                          </div>
                          {book.is_overdue && (
                            <div className="overdue-info">
                              <span className="days-overdue">{book.days_overdue} days overdue</span>
                              {book.fine_amount > 0 && (
                                <span className="fine-amount">
                                  <DollarSign size={12} />
                                  ${book.fine_amount.toFixed(2)}
                                </span>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Fine Summary */}
              {selectedBooks.length > 0 && totalFine > 0 && (
                <div className="fine-summary">
                  <div className="fine-header">
                    <DollarSign size={20} />
                    <h3>Fine Summary</h3>
                  </div>
                  <div className="fine-details">
                    <p>Total Fine for Selected Books: <strong>${totalFine.toFixed(2)}</strong></p>
                    <small>Fine will be added to user's account upon return</small>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              {userInfo && userInfo.current_books.length > 0 && (
                <div className="form-actions">
                  <button
                    type="button"
                    className="btn btn-secondary"
                    onClick={handleRenewal}
                    disabled={isRenewing || selectedBooks.length === 0 || userInfo.total_fine > 0 || selectedBooks.some(book => book.is_overdue)}
                  >
                    <RotateCcw size={16} />
                    {isRenewing ? 'Renewing...' : `Renew ${selectedBooks.length} Book${selectedBooks.length !== 1 ? 's' : ''}`}
                  </button>
                  <button
                    type="submit"
                    className="btn btn-primary"
                    disabled={isSubmitting || selectedBooks.length === 0}
                  >
                    {isSubmitting ? 'Processing...' : `Return ${selectedBooks.length} Book${selectedBooks.length !== 1 ? 's' : ''}`}
                  </button>
                </div>
              )}
            </form>
          </div>
        </div>

        {/* User Information Panel */}
        {userInfo && (
          <div className="user-info-section">
            <div className="user-card">
              <div className="user-header">
                <div className="user-avatar">
                  <User size={24} />
                </div>
                <div className="user-details">
                  <h3>{userInfo.user.name}</h3>
                  <p>ID: {userInfo.user.user_id}</p>
                  <p>{userInfo.user.email}</p>
                  {userInfo.user.college && (
                    <p>{userInfo.user.college} - {userInfo.user.department}</p>
                  )}
                </div>
                <div className="user-status">
                  <div className="status-badge info">
                    <Book size={16} />
                    {userInfo.current_books.length} Books
                  </div>
                </div>
              </div>

              {/* Current Fine Amount */}
              {userInfo.total_fine > 0 && (
                <div className="fine-alert">
                  <AlertCircle size={16} />
                  <span>Outstanding Fine: ${userInfo.total_fine.toFixed(2)}</span>
                </div>
              )}

              {/* Summary Stats */}
              <div className="stats-grid">
                <div className="stat-card">
                  <div className="stat-icon">
                    <Book size={20} />
                  </div>
                  <div className="stat-info">
                    <span className="stat-number">{userInfo.current_books.length}</span>
                    <span className="stat-label">Books Borrowed</span>
                  </div>
                </div>

                <div className="stat-card">
                  <div className="stat-icon overdue">
                    <Clock size={20} />
                  </div>
                  <div className="stat-info">
                    <span className="stat-number">
                      {userInfo.current_books.filter(book => book.is_overdue).length}
                    </span>
                    <span className="stat-label">Overdue Books</span>
                  </div>
                </div>

                <div className="stat-card">
                  <div className="stat-icon fine">
                    <DollarSign size={20} />
                  </div>
                  <div className="stat-info">
                    <span className="stat-number">
                      ${userInfo.current_books.reduce((sum, book) => sum + (book.fine_amount || 0), 0).toFixed(2)}
                    </span>
                    <span className="stat-label">Pending Fines</span>
                  </div>
                </div>
              </div>

              {/* No Books Message */}
              {userInfo.current_books.length === 0 && (
                <div className="no-books-message">
                  <CheckCircle size={48} />
                  <h3>No Books to Return</h3>
                  <p>This user has no currently borrowed books.</p>
                </div>
              )}

              {/* Recent Returns */}
              {userInfo.borrowing_history.length > 0 && (
                <div className="section">
                  <h4>Recent Returns</h4>
                  <div className="history-list">
                    {userInfo.borrowing_history.filter(item => item.status === 'returned').slice(0, 3).map((item, index) => (
                      <div key={index} className="history-item">
                        <div className="book-info">
                          <strong>{item.book_title}</strong>
                          <span>by {item.author}</span>
                        </div>
                        <div className="history-dates">
                          <span>Returned: {new Date(item.return_date).toLocaleDateString()}</span>
                          {item.fine_amount > 0 && (
                            <span className="fine">Fine: ${item.fine_amount.toFixed(2)}</span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ReturnBook
