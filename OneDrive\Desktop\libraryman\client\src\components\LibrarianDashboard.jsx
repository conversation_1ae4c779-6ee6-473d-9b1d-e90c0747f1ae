import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import Sidebar from './Sidebar'
import LibrarianHome from './librarian/LibrarianHome'
// Import admin components for librarian use (with appropriate permissions)
import ManageBooks from './admin/ManageBooks'
import ManageEbooks from './admin/ManageEbooks'
import IssueBook from './admin/IssueBook'
import ReturnBook from './admin/ReturnBook'
import CirculationHistory from './admin/CirculationHistory'
import ManageStudents from './admin/ManageStudents'
import ManageColleges from './admin/ManageColleges'
import ManageDepartments from './admin/ManageDepartments'
import NewsClippings from './admin/NewsClippings'
import FineManagement from './admin/FineManagement'
import PaymentManagement from './admin/PaymentManagement'
import GateEntryManagement from './admin/GateEntryManagement'
import FineReports from './admin/FineReports'
import CounterReports from './admin/CounterReports'
import GateEntryReports from './admin/GateEntryReports'

const LibrarianDashboard = () => {
  return (
    <div className="dashboard">
      <Sidebar userRole="librarian" />
      <div className="main-content">
        <Routes>
          <Route path="/" element={<LibrarianHome />} />
          {/* Management Routes */}
          <Route path="/books" element={<ManageBooks />} />
          <Route path="/ebooks" element={<ManageEbooks />} />
          <Route path="/students" element={<ManageStudents userRole="librarian" />} />
          <Route path="/colleges" element={<ManageColleges userRole="librarian" />} />
          <Route path="/departments" element={<ManageDepartments userRole="librarian" />} />
          <Route path="/news-clippings" element={<NewsClippings />} />
          {/* Circulation Routes */}
          <Route path="/issue-book" element={<IssueBook />} />
          <Route path="/return-book" element={<ReturnBook />} />
          <Route path="/circulation-history" element={<CirculationHistory />} />
          <Route path="/fine-management" element={<FineManagement />} />
          <Route path="/payment-management" element={<PaymentManagement />} />
          <Route path="/gate-entry" element={<GateEntryManagement />} />
          <Route path="/fine-reports" element={<FineReports />} />
          <Route path="/counter-reports" element={<CounterReports />} />
          <Route path="/gate-entry-reports" element={<GateEntryReports />} />
          <Route path="*" element={<Navigate to="/librarian" />} />
        </Routes>
      </div>
    </div>
  )
}

export default LibrarianDashboard
