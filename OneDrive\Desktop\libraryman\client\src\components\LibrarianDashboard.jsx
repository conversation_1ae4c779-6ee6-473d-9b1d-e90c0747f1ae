import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import Sidebar from './Sidebar'
import ManageBooks from './librarian/ManageBooks'
import ManageEbooks from './librarian/ManageEbooks'
import IssueBook from './librarian/IssueBook'
import ReturnBook from './librarian/ReturnBook'
import CirculationHistory from './librarian/CirculationHistory'
import LibrarianHome from './librarian/LibrarianHome'

const LibrarianDashboard = () => {
  return (
    <div className="dashboard">
      <Sidebar userRole="librarian" />
      <div className="main-content">
        <Routes>
          <Route path="/" element={<LibrarianHome />} />
          <Route path="/books" element={<ManageBooks />} />
          <Route path="/ebooks" element={<ManageEbooks />} />
          <Route path="/issue-book" element={<IssueBook />} />
          <Route path="/return-book" element={<ReturnBook />} />
          <Route path="/circulation-history" element={<CirculationHistory />} />
          <Route path="*" element={<Navigate to="/librarian" />} />
        </Routes>
      </div>
    </div>
  )
}

export default LibrarianDashboard
