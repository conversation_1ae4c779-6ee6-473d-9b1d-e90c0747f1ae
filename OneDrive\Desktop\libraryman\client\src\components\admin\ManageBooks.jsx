import React, { useState, useEffect } from 'react'
import { Plus, Upload, Download, Search, Edit, Trash2, Book } from 'lucide-react'
import axios from 'axios'

const ManageBooks = () => {
  const [books, setBooks] = useState([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [showBulkUpload, setShowBulkUpload] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  const [formData, setFormData] = useState({
    access_no: '',
    title: '',
    author: '',
    publisher: '',
    department: '',
    category: '',
    location: '',
    number_of_copies: 1,
    isbn: ''
  })

  const [bulkFile, setBulkFile] = useState(null)

  useEffect(() => {
    fetchBooks()
  }, [currentPage, searchTerm])

  const fetchBooks = async () => {
    try {
      const response = await axios.get('/admin/books', {
        params: {
          page: currentPage,
          per_page: 10,
          search: searchTerm
        }
      })
      setBooks(response.data.books)
      setTotalPages(response.data.pagination.pages)
    } catch (error) {
      console.error('Failed to fetch books:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      await axios.post('/admin/books', formData)
      alert('Book added successfully!')
      setShowAddForm(false)
      setFormData({
        access_no: '',
        title: '',
        author: '',
        publisher: '',
        department: '',
        category: '',
        location: '',
        number_of_copies: 1,
        isbn: ''
      })
      fetchBooks()
    } catch (error) {
      alert(error.response?.data?.error || 'Failed to add book')
    }
  }

  const handleBulkUpload = async (e) => {
    e.preventDefault()
    if (!bulkFile) {
      alert('Please select a file')
      return
    }

    const formData = new FormData()
    formData.append('file', bulkFile)

    try {
      const response = await axios.post('/admin/books/bulk', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      alert(`Successfully added ${response.data.created_books.length} books`)
      setShowBulkUpload(false)
      setBulkFile(null)
      fetchBooks()
    } catch (error) {
      alert(error.response?.data?.error || 'Failed to upload books')
    }
  }

  const downloadSample = async () => {
    try {
      const response = await axios.get('/admin/books/sample', {
        responseType: 'blob'
      })

      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', 'books_sample.xlsx')
      document.body.appendChild(link)
      link.click()
      link.remove()
    } catch (error) {
      alert('Failed to download sample file')
    }
  }

  if (loading) {
    return <div className="loading">Loading books...</div>
  }

  return (
    <div className="manage-books">
      <div className="page-header">
        <div>
          <h1>Manage Books</h1>
          <p>Add and manage physical book collection</p>
        </div>
        <div className="header-actions">
          <button
            className="btn btn-primary"
            onClick={() => setShowAddForm(true)}
          >
            <Plus size={16} />
            Add Book
          </button>
          <button
            className="btn btn-secondary"
            onClick={() => setShowBulkUpload(true)}
          >
            <Upload size={16} />
            Bulk Upload
          </button>
          <button
            className="btn btn-secondary"
            onClick={downloadSample}
          >
            <Download size={16} />
            Sample Sheet
          </button>
        </div>
      </div>

      <div className="filters">
        <div className="search-box">
          <Search size={16} />
          <input
            type="text"
            placeholder="Search books..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <div className="books-table">
        <table>
          <thead>
            <tr>
              <th>Access No</th>
              <th>Title</th>
              <th>Author</th>
              <th>Publisher</th>
              <th>Department</th>
              <th>Category</th>
              <th>Location</th>
              <th>Copies</th>
              <th>Available</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {books.map((book) => (
              <tr key={book.id}>
                <td>{book.access_no}</td>
                <td>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                    <Book size={16} color="#667eea" />
                    {book.title}
                  </div>
                </td>
                <td>{book.author}</td>
                <td>{book.publisher}</td>
                <td>{book.department}</td>
                <td>{book.category}</td>
                <td>{book.location}</td>
                <td>{book.number_of_copies}</td>
                <td>{book.available_copies}</td>
                <td>
                  <div className="actions">
                    <button className="btn-icon">
                      <Edit size={14} />
                    </button>
                    <button className="btn-icon danger">
                      <Trash2 size={14} />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="pagination">
        <button
          disabled={currentPage === 1}
          onClick={() => setCurrentPage(currentPage - 1)}
        >
          Previous
        </button>
        <span>Page {currentPage} of {totalPages}</span>
        <button
          disabled={currentPage === totalPages}
          onClick={() => setCurrentPage(currentPage + 1)}
        >
          Next
        </button>
      </div>

      {/* Add Book Modal */}
      {showAddForm && (
        <div className="modal">
          <div className="modal-content">
            <div className="modal-header">
              <h2>Add New Book</h2>
              <button onClick={() => setShowAddForm(false)}>×</button>
            </div>
            <form onSubmit={handleSubmit}>
              <div className="form-grid">
                <div className="form-group">
                  <label>Access Number</label>
                  <input
                    type="text"
                    name="access_no"
                    value={formData.access_no}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="form-group">
                  <label>Book Title</label>
                  <input
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="form-group">
                  <label>Author</label>
                  <input
                    type="text"
                    name="author"
                    value={formData.author}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="form-group">
                  <label>Publisher</label>
                  <input
                    type="text"
                    name="publisher"
                    value={formData.publisher}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="form-group">
                  <label>Department</label>
                  <input
                    type="text"
                    name="department"
                    value={formData.department}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="form-group">
                  <label>Category</label>
                  <input
                    type="text"
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="form-group">
                  <label>Location</label>
                  <input
                    type="text"
                    name="location"
                    value={formData.location}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="form-group">
                  <label>Number of Copies</label>
                  <input
                    type="number"
                    name="number_of_copies"
                    value={formData.number_of_copies}
                    onChange={handleInputChange}
                    min="1"
                    required
                  />
                </div>
                <div className="form-group">
                  <label>ISBN</label>
                  <input
                    type="text"
                    name="isbn"
                    value={formData.isbn}
                    onChange={handleInputChange}
                  />
                </div>
              </div>
              <div className="modal-actions">
                <button type="button" onClick={() => setShowAddForm(false)}>
                  Cancel
                </button>
                <button type="submit" className="btn btn-primary">
                  Add Book
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Bulk Upload Modal */}
      {showBulkUpload && (
        <div className="modal">
          <div className="modal-content">
            <div className="modal-header">
              <h2>Bulk Upload Books</h2>
              <button onClick={() => setShowBulkUpload(false)}>×</button>
            </div>
            <form onSubmit={handleBulkUpload}>
              <div className="form-group">
                <label>Excel File</label>
                <input
                  type="file"
                  accept=".xlsx,.xls"
                  onChange={(e) => setBulkFile(e.target.files[0])}
                  required
                />
                <small>
                  Excel file should contain columns: access_no, title, author, publisher, department, category, location, number_of_copies, isbn
                </small>
              </div>
              <div className="modal-actions">
                <button type="button" onClick={() => setShowBulkUpload(false)}>
                  Cancel
                </button>
                <button type="button" onClick={downloadSample} className="btn btn-secondary">
                  Download Sample
                </button>
                <button type="submit" className="btn btn-primary">
                  Upload Books
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}

export default ManageBooks
