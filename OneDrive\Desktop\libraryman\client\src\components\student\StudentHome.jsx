import React, { useState, useEffect } from 'react'
import { Book, Calendar, AlertCircle, CheckCircle } from 'lucide-react'
import axios from 'axios'

const StudentHome = () => {
  const [dashboardData, setDashboardData] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      const response = await axios.get('/student/dashboard')
      setDashboardData(response.data)
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return <div className="loading">Loading dashboard...</div>
  }

  if (!dashboardData) {
    return <div>Failed to load dashboard data</div>
  }

  const { user, stats, borrowed_books } = dashboardData

  return (
    <div className="student-home">
      <div className="welcome-section">
        <h1>Welcome back, {user.name}!</h1>
        <div className="user-details">
          <p><strong>Student ID:</strong> {user.user_id}</p>
          <p><strong>Email:</strong> {user.email}</p>
          <p><strong>College:</strong> {user.college}</p>
          <p><strong>Department:</strong> {user.department}</p>
          <p><strong>Account Valid Until:</strong> {new Date(user.validity_date).toLocaleDateString()}</p>
        </div>
      </div>

      <div className="stats-section">
        <div className="stat-card blue">
          <div className="stat-icon">
            <Book size={24} />
          </div>
          <div className="stat-content">
            <h3>{stats.books_borrowed}</h3>
            <p className="stat-title">Currently Borrowed</p>
          </div>
        </div>
        
        <div className="stat-card green">
          <div className="stat-icon">
            <CheckCircle size={24} />
          </div>
          <div className="stat-content">
            <h3>{stats.total_books_read}</h3>
            <p className="stat-title">Total Books Read</p>
          </div>
        </div>
        
        <div className="stat-card red">
          <div className="stat-icon">
            <AlertCircle size={24} />
          </div>
          <div className="stat-content">
            <h3>₹{stats.total_fines}</h3>
            <p className="stat-title">Total Fines</p>
          </div>
        </div>
      </div>

      <div className="borrowed-books-section">
        <h2>Currently Borrowed Books</h2>
        {borrowed_books.length === 0 ? (
          <div className="empty-state">
            <Book size={48} color="#ccc" />
            <p>No books currently borrowed</p>
            <a href="/student/books" className="btn btn-primary">Browse Books</a>
          </div>
        ) : (
          <div className="borrowed-books-grid">
            {borrowed_books.map((book) => (
              <div key={book.id} className={`book-card ${book.is_overdue ? 'overdue' : ''}`}>
                <div className="book-info">
                  <h3>{book.book_title}</h3>
                  <p>by {book.book_author}</p>
                </div>
                <div className="book-dates">
                  <div className="date-info">
                    <Calendar size={14} />
                    <span>Issued: {new Date(book.issue_date).toLocaleDateString()}</span>
                  </div>
                  <div className="date-info">
                    <Calendar size={14} />
                    <span>Due: {new Date(book.due_date).toLocaleDateString()}</span>
                  </div>
                  {book.is_overdue ? (
                    <div className="overdue-warning">
                      <AlertCircle size={14} />
                      <span>Overdue!</span>
                    </div>
                  ) : (
                    <div className="days-remaining">
                      <span>{book.days_remaining} days remaining</span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="quick-actions">
        <h2>Quick Actions</h2>
        <div className="action-grid">
          <a href="/student/books" className="action-card">
            <Book size={24} />
            <h3>Browse Books</h3>
            <p>Find and explore our book collection</p>
          </a>
          <a href="/student/ebooks" className="action-card">
            <Book size={24} />
            <h3>Browse E-books</h3>
            <p>Access digital books and resources</p>
          </a>
          <a href="/student/history" className="action-card">
            <Calendar size={24} />
            <h3>Borrowing History</h3>
            <p>View your past borrowing records</p>
          </a>
        </div>
      </div>
    </div>
  )
}

export default StudentHome
