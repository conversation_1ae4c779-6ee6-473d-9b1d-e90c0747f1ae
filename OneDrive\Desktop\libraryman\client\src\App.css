/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  width: 100%;
  overflow-x: hidden;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  color: #333;
}

#root {
  height: 100vh;
  width: 100vw;
}

.App {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
}

/* Loading */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 18px;
  color: #666;
}

/* Login Styles */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-icon {
  color: #667eea;
  margin-bottom: 1rem;
}

.login-header h1 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.login-header p {
  color: #666;
  font-size: 0.9rem;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 12px;
  color: #666;
  z-index: 1;
}

.input-group input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.input-group input:focus {
  outline: none;
  border-color: #667eea;
}

.login-button {
  background: #667eea;
  color: white;
  border: none;
  padding: 12px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-top: 1rem;
}

.login-button:hover:not(:disabled) {
  background: #5a6fd8;
}

.login-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #fee;
  color: #c53030;
  padding: 0.75rem;
  border-radius: 6px;
  font-size: 0.9rem;
  border: 1px solid #fed7d7;
}

.login-footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e1e5e9;
}

.login-footer p {
  color: #666;
  font-size: 0.8rem;
}

/* Dashboard Layout */
.dashboard {
  display: flex;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

.main-content {
  flex: 1;
  margin-left: 280px;
  padding: 2rem;
  background: #f8f9fa;
  overflow-y: auto;
  height: 100vh;
}

/* Sidebar Styles */
.sidebar {
  width: 280px;
  background: #2d3748;
  color: white;
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  overflow-y: auto;
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid #4a5568;
}

.sidebar-logo {
  color: #667eea;
  margin-bottom: 0.5rem;
}

.sidebar-header h2 {
  font-size: 1.2rem;
  margin-bottom: 1rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #a0aec0;
}

.user-info small {
  display: block;
  font-size: 0.8rem;
  color: #718096;
}

.sidebar-nav {
  flex: 1;
  padding: 1rem 0;
}

.nav-section {
  margin-bottom: 1rem;
}

.nav-toggle {
  width: 100%;
  background: none;
  border: none;
  color: white;
  padding: 0.75rem 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 0.9rem;
}

.nav-toggle:hover {
  background: #4a5568;
}

.nav-toggle.expanded {
  background: #4a5568;
}

.nav-toggle span {
  flex: 1;
  text-align: left;
}

.nav-submenu {
  background: #1a202c;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 2rem;
  color: #a0aec0;
  text-decoration: none;
  transition: all 0.2s ease;
  font-size: 0.85rem;
}

.nav-link:hover {
  background: #2d3748;
  color: white;
}

.nav-link.active {
  background: #667eea;
  color: white;
}

.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid #4a5568;
}

.logout-button {
  width: 100%;
  background: #e53e3e;
  border: none;
  color: white;
  padding: 0.75rem;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 0.9rem;
}

.logout-button:hover {
  background: #c53030;
}

/* Page Content Styles */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.page-header h1 {
  font-size: 1.8rem;
  color: #2d3748;
  margin: 0;
}

.page-header p {
  color: #718096;
  margin: 0.5rem 0 0 0;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover {
  background: #5a6fd8;
}

.btn-secondary {
  background: #e2e8f0;
  color: #4a5568;
}

.btn-secondary:hover {
  background: #cbd5e0;
}

.btn-icon {
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  background: white;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
  color: #4a5568;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  min-height: 32px;
}

.btn-icon:hover {
  background: #667eea;
  color: white;
  border-color: #667eea;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-icon.danger {
  color: #e53e3e;
  border-color: #fed7d7;
}

.btn-icon.danger:hover {
  background: #e53e3e;
  color: white;
  border-color: #e53e3e;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(229, 62, 62, 0.2);
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  border-left: 4px solid;
}

.stat-card.blue { border-left-color: #3182ce; }
.stat-card.green { border-left-color: #38a169; }
.stat-card.purple { border-left-color: #805ad5; }
.stat-card.orange { border-left-color: #dd6b20; }
.stat-card.red { border-left-color: #e53e3e; }
.stat-card.teal { border-left-color: #319795; }

.stat-icon {
  padding: 0.75rem;
  border-radius: 8px;
  background: #f7fafc;
}

.stat-card.blue .stat-icon { background: #ebf8ff; color: #3182ce; }
.stat-card.green .stat-icon { background: #f0fff4; color: #38a169; }
.stat-card.purple .stat-icon { background: #faf5ff; color: #805ad5; }
.stat-card.orange .stat-icon { background: #fffaf0; color: #dd6b20; }
.stat-card.red .stat-icon { background: #fed7d7; color: #e53e3e; }
.stat-card.teal .stat-icon { background: #e6fffa; color: #319795; }

.stat-content h3 {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: #2d3748;
}

.stat-title {
  font-weight: 500;
  color: #4a5568;
  margin: 0.25rem 0;
}

.stat-description {
  color: #718096;
  font-size: 0.8rem;
}

/* Table Styles */
.students-table, .books-table, .circulation-table, .colleges-table, .departments-table, .librarians-table, .ebooks-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.students-table table, .books-table table, .circulation-table table, .colleges-table table, .departments-table table, .librarians-table table, .ebooks-table table {
  width: 100%;
  border-collapse: collapse;
}

.students-table th, .books-table th, .circulation-table th, .colleges-table th, .departments-table th, .librarians-table th, .ebooks-table th {
  background: #f7fafc;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #4a5568;
  border-bottom: 1px solid #e2e8f0;
}

.students-table td, .books-table td, .circulation-table td, .colleges-table td, .departments-table td, .librarians-table td, .ebooks-table td {
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.students-table tr:hover, .books-table tr:hover, .circulation-table tr:hover, .colleges-table tr:hover, .departments-table tr:hover, .librarians-table tr:hover, .ebooks-table tr:hover {
  background: #f7fafc;
}

.status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status.active {
  background: #c6f6d5;
  color: #22543d;
}

.status.inactive {
  background: #fed7d7;
  color: #742a2a;
}

.actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  justify-content: center;
}

/* Filters */
.filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  align-items: center;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
  flex: 1;
  max-width: 400px;
}

.search-box svg {
  position: absolute;
  left: 12px;
  color: #718096;
}

.search-box input {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.9rem;
}

.search-box input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-select {
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.9rem;
  background: white;
  min-width: 200px;
}

.filter-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
}

.pagination button {
  padding: 0.5rem 1rem;
  border: 1px solid #e2e8f0;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination button:hover:not(:disabled) {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.modal-header h2 {
  margin: 0;
  color: #2d3748;
}

.modal-header button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #718096;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

/* Form Styles */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  padding: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #4a5568;
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group small {
  color: #718096;
  font-size: 0.8rem;
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.error-text {
  color: #ef4444;
  font-size: 12px;
  margin-top: 0.25rem;
}

.password-input {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input input {
  flex: 1;
  padding-right: 2.5rem;
}

.password-toggle {
  position: absolute;
  right: 0.75rem;
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-toggle:hover {
  color: #374151;
}

/* Issue Book Styles */
.issue-book-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 2rem;
}

.issue-form-section {
  display: flex;
  flex-direction: column;
}

.form-card {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-card h2 {
  margin-bottom: 1.5rem;
  color: #1a202c;
}

.search-input {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input svg {
  position: absolute;
  left: 0.75rem;
  color: #6b7280;
}

.search-input input {
  padding-left: 2.5rem;
  width: 100%;
}

.date-input {
  position: relative;
  display: flex;
  align-items: center;
}

.date-input svg {
  position: absolute;
  left: 0.75rem;
  color: #6b7280;
  z-index: 1;
}

.date-input input {
  padding-left: 2.5rem;
  width: 100%;
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
  z-index: 10;
}

.search-result-item {
  padding: 0.75rem;
  cursor: pointer;
  border-bottom: 1px solid #f7fafc;
}

.search-result-item:hover {
  background: #f7fafc;
}

.search-result-item:last-child {
  border-bottom: none;
}

.book-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.book-info strong {
  color: #1a202c;
}

.book-info span {
  color: #4a5568;
  font-size: 0.9rem;
}

.book-info small {
  color: #718096;
  font-size: 0.8rem;
}

.loading-spinner {
  position: absolute;
  right: 0.75rem;
  color: #667eea;
  font-size: 0.8rem;
}

/* User Info Panel Styles */
.user-info-section {
  display: flex;
  flex-direction: column;
}

.user-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.user-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.user-avatar {
  background: #667eea;
  color: white;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-details {
  flex: 1;
}

.user-details h3 {
  margin: 0 0 0.25rem 0;
  color: #1a202c;
}

.user-details p {
  margin: 0.125rem 0;
  color: #4a5568;
  font-size: 0.9rem;
}

.user-status {
  display: flex;
  align-items: center;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-badge.success {
  background: #c6f6d5;
  color: #22543d;
}

.status-badge.danger {
  background: #fed7d7;
  color: #c53030;
}

.fine-alert {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #fed7d7;
  color: #c53030;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.section {
  margin-bottom: 1.5rem;
}

.section h4 {
  margin: 0 0 1rem 0;
  color: #1a202c;
  font-size: 1rem;
}

.no-data {
  color: #718096;
  font-style: italic;
  text-align: center;
  padding: 1rem;
}

.books-list, .history-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.book-item, .history-item {
  background: #f7fafc;
  border-radius: 6px;
  padding: 1rem;
  border-left: 4px solid #e2e8f0;
}

.book-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.book-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.book-details strong {
  color: #1a202c;
  font-size: 0.9rem;
}

.book-details span {
  color: #4a5568;
  font-size: 0.8rem;
}

.book-details small {
  color: #718096;
  font-size: 0.75rem;
}

.book-dates {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.date-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.date-info span {
  font-size: 0.75rem;
  color: #718096;
}

.overdue-badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: #fed7d7;
  color: #c53030;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

.fine-amount {
  background: #c53030;
  color: white;
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
  margin-left: 0.25rem;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.history-dates {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.history-dates span {
  font-size: 0.75rem;
  color: #718096;
}

.status {
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status.returned {
  background: #c6f6d5;
  color: #22543d;
}

.status.overdue {
  background: #fed7d7;
  color: #c53030;
}

.fine {
  color: #c53030;
  font-weight: 500;
}

/* Responsive Design for Issue Book */
@media (max-width: 768px) {
  .issue-book-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .user-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .book-item {
    flex-direction: column;
    gap: 0.75rem;
  }

  .book-dates {
    align-items: flex-start;
  }
}

/* Student Dashboard Styles */
.student-dashboard {
  height: 100vh;
  width: 100vw;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.student-header {
  background: white;
  padding: 1rem 2rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.student-info h1 {
  margin: 0;
  color: #2d3748;
  font-size: 1.5rem;
}

.student-info p {
  margin: 0.25rem 0 0 0;
  color: #718096;
}

.student-nav {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.student-nav a {
  color: #4a5568;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.student-nav a:hover {
  background: #f7fafc;
}

.student-nav button {
  background: #e53e3e;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
}

.student-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

.student-home {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.welcome-section h1 {
  margin: 0 0 1rem 0;
  color: #2d3748;
}

.user-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.user-details p {
  margin: 0;
  color: #4a5568;
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.borrowed-books-section {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.borrowed-books-section h2 {
  margin: 0 0 1.5rem 0;
  color: #2d3748;
}

.empty-state {
  text-align: center;
  padding: 3rem;
  color: #718096;
}

.empty-state p {
  margin: 1rem 0;
  font-size: 1.1rem;
}

.borrowed-books-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.book-card {
  background: #f7fafc;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.book-card.overdue {
  border-left-color: #e53e3e;
  background: #fed7d7;
}

.book-info h3 {
  margin: 0 0 0.5rem 0;
  color: #2d3748;
}

.book-info p {
  margin: 0;
  color: #718096;
}

.book-dates {
  margin-top: 1rem;
}

.date-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: #4a5568;
}

.overdue-warning {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #c53030;
  font-weight: 500;
  font-size: 0.9rem;
}

.days-remaining {
  color: #38a169;
  font-weight: 500;
  font-size: 0.9rem;
}

.quick-actions {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.quick-actions h2 {
  margin: 0 0 1.5rem 0;
  color: #2d3748;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.action-card {
  background: #f7fafc;
  padding: 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  color: inherit;
  transition: all 0.2s ease;
  border: 1px solid #e2e8f0;
}

.action-card:hover {
  background: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.action-card h3 {
  margin: 0.5rem 0;
  color: #2d3748;
}

.action-card p {
  margin: 0;
  color: #718096;
  font-size: 0.9rem;
}

/* Content Placeholder */
.content-placeholder {
  background: white;
  padding: 3rem;
  border-radius: 8px;
  text-align: center;
  color: #718096;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.content-placeholder p {
  font-size: 1.1rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
    padding: 1rem;
  }

  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .student-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .student-nav {
    width: 100%;
    justify-content: space-between;
  }
}
