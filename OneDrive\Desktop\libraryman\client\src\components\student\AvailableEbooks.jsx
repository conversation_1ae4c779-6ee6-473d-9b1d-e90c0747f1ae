import React, { useState, useEffect } from 'react'
import { Monitor, Search, Filter, Download, User, Calendar, FileText, HardDrive } from 'lucide-react'
import axios from 'axios'

const AvailableEbooks = () => {
  const [ebooks, setEbooks] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filters, setFilters] = useState({
    category: 'all',
    format: 'all'
  })
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const ebooksPerPage = 12

  useEffect(() => {
    fetchEbooks()
  }, [currentPage, searchTerm, filters])

  const fetchEbooks = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: currentPage,
        per_page: ebooksPerPage,
        search: searchTerm,
        ...filters
      })

      const response = await axios.get(`/student/ebooks?${params.toString()}`)
      setEbooks(response.data.ebooks)
      setTotalPages(Math.ceil(response.data.total / ebooksPerPage))
    } catch (error) {
      console.error('Failed to fetch e-books:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (e) => {
    setSearchTerm(e.target.value)
    setCurrentPage(1)
  }

  const handleFilterChange = (e) => {
    const { name, value } = e.target
    setFilters(prev => ({
      ...prev,
      [name]: value
    }))
    setCurrentPage(1)
  }

  const handleDownload = async (ebookId, title) => {
    try {
      const response = await axios.get(`/student/ebooks/${ebookId}/download`, {
        responseType: 'blob'
      })
      
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `${title}.pdf`)
      document.body.appendChild(link)
      link.click()
      link.remove()
      
      // Refresh the list to update download count
      fetchEbooks()
    } catch (error) {
      console.error('Failed to download e-book:', error)
      alert('Failed to download e-book')
    }
  }

  const formatFileSize = (bytes) => {
    if (!bytes) return 'Unknown'
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  return (
    <div className="available-ebooks">
      <div className="page-header">
        <div>
          <h1>Available E-books</h1>
          <p>Browse and download digital books from our collection</p>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="search-filters">
        <div className="search-bar">
          <Search size={20} />
          <input
            type="text"
            placeholder="Search e-books by title, author, or description..."
            value={searchTerm}
            onChange={handleSearch}
          />
        </div>
        <div className="filters">
          <div className="filter-group">
            <Filter size={16} />
            <select
              name="category"
              value={filters.category}
              onChange={handleFilterChange}
            >
              <option value="all">All Categories</option>
              <option value="fiction">Fiction</option>
              <option value="non-fiction">Non-Fiction</option>
              <option value="science">Science</option>
              <option value="technology">Technology</option>
              <option value="history">History</option>
              <option value="literature">Literature</option>
            </select>
          </div>
          <div className="filter-group">
            <select
              name="format"
              value={filters.format}
              onChange={handleFilterChange}
            >
              <option value="all">All Formats</option>
              <option value="pdf">PDF</option>
              <option value="epub">EPUB</option>
              <option value="mobi">MOBI</option>
            </select>
          </div>
        </div>
      </div>

      {/* E-books Grid */}
      <div className="ebooks-section">
        {loading ? (
          <div className="loading-state">
            <div className="loading-spinner"></div>
            <p>Loading e-books...</p>
          </div>
        ) : ebooks.length === 0 ? (
          <div className="no-data">
            <Monitor size={48} />
            <h3>No E-books Found</h3>
            <p>No e-books match your search criteria. Try adjusting your filters.</p>
          </div>
        ) : (
          <div className="ebooks-grid">
            {ebooks.map((ebook) => (
              <div key={ebook.id} className="ebook-card">
                <div className="ebook-header">
                  <div className="ebook-icon">
                    <Monitor size={24} />
                  </div>
                  <div className="format-badge">
                    {ebook.file_format?.toUpperCase() || 'PDF'}
                  </div>
                </div>
                
                <div className="ebook-content">
                  <h3 className="ebook-title">{ebook.title}</h3>
                  <div className="ebook-details">
                    <div className="detail-item">
                      <User size={14} />
                      <span>{ebook.author}</span>
                    </div>
                    {ebook.description && (
                      <div className="detail-item description">
                        <FileText size={14} />
                        <span>{ebook.description}</span>
                      </div>
                    )}
                    <div className="detail-item">
                      <HardDrive size={14} />
                      <span>{formatFileSize(ebook.file_size)}</span>
                    </div>
                    <div className="detail-item">
                      <Download size={14} />
                      <span>{ebook.download_count || 0} downloads</span>
                    </div>
                    <div className="detail-item">
                      <Calendar size={14} />
                      <span>Added: {new Date(ebook.created_at).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
                
                <div className="ebook-footer">
                  <button
                    className="btn btn-primary download-btn"
                    onClick={() => handleDownload(ebook.id, ebook.title)}
                  >
                    <Download size={16} />
                    Download
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="pagination">
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            Previous
          </button>
          
          <div className="page-numbers">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              let pageNum
              if (totalPages <= 5) {
                pageNum = i + 1
              } else if (currentPage <= 3) {
                pageNum = i + 1
              } else if (currentPage >= totalPages - 2) {
                pageNum = totalPages - 4 + i
              } else {
                pageNum = currentPage - 2 + i
              }
              
              return (
                <button
                  key={pageNum}
                  onClick={() => setCurrentPage(pageNum)}
                  className={currentPage === pageNum ? 'active' : ''}
                >
                  {pageNum}
                </button>
              )
            })}
          </div>
          
          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
          >
            Next
          </button>
        </div>
      )}
    </div>
  )
}

export default AvailableEbooks
