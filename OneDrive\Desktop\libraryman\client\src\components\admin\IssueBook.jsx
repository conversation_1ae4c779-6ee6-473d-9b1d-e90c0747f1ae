import React, { useState, useEffect } from 'react'
import { Search, User, Book, Calendar, AlertCircle, CheckCircle, Clock } from 'lucide-react'
import axios from 'axios'

const IssueBook = () => {
  const [userInfo, setUserInfo] = useState(null)
  const [userLoading, setUserLoading] = useState(false)
  const [bookSearch, setBookSearch] = useState('')
  const [searchResults, setSearchResults] = useState([])
  const [selectedBook, setSelectedBook] = useState(null)
  const [issueDate, setIssueDate] = useState('')
  const [dueDate, setDueDate] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState({})

  const [formData, setFormData] = useState({
    userId: '',
    bookId: '',
    dueDate: ''
  })

  useEffect(() => {
    // Set default dates
    const today = new Date()
    const defaultDue = new Date()
    defaultDue.setDate(today.getDate() + 14) // 14 days from today

    setIssueDate(today.toISOString().split('T')[0])
    setDueDate(defaultDue.toISOString().split('T')[0])
    setFormData(prev => ({ ...prev, dueDate: defaultDue.toISOString().split('T')[0] }))
  }, [])

  const handleUserSearch = async (userId) => {
    if (!userId.trim()) {
      setUserInfo(null)
      setErrors(prev => ({ ...prev, user: '' }))
      return
    }

    setUserLoading(true)
    try {
      console.log('Searching for user:', userId)
      const response = await axios.get(`/admin/circulation/user/${userId}`)
      console.log('User search response:', response.data)
      setUserInfo(response.data)
      setFormData(prev => ({ ...prev, userId: userId }))
      setErrors(prev => ({ ...prev, user: '' }))
    } catch (error) {
      console.error('User search failed:', error)
      console.error('Error details:', error.response?.data)
      setUserInfo(null)

      let errorMessage = 'User not found'
      if (error.response?.data?.available_student_ids) {
        errorMessage += `\nAvailable student IDs: ${error.response.data.available_student_ids.join(', ')}`
      }

      setErrors(prev => ({
        ...prev,
        user: errorMessage
      }))
    } finally {
      setUserLoading(false)
    }
  }

  const handleBookSearch = async (searchTerm) => {
    setBookSearch(searchTerm)
    if (searchTerm.length < 2) {
      setSearchResults([])
      return
    }

    try {
      console.log('Searching for books with term:', searchTerm)
      const response = await axios.get(`/admin/books/search?search=${searchTerm}`)
      console.log('Book search response:', response.data)
      setSearchResults(response.data.books || [])
    } catch (error) {
      console.error('Book search failed:', error)
      console.error('Error details:', error.response?.data)
      setSearchResults([])
    }
  }

  const selectBook = (book) => {
    setSelectedBook(book)
    setFormData(prev => ({ ...prev, bookId: book.id }))
    setBookSearch(`${book.title} by ${book.author}`)
    setSearchResults([])
    setErrors(prev => ({ ...prev, book: '' }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    // Validation
    const newErrors = {}
    if (!userInfo) {
      newErrors.user = 'Please search and select a valid user'
    }
    if (!selectedBook) {
      newErrors.book = 'Please search and select a book'
    }
    if (!formData.dueDate) {
      newErrors.dueDate = 'Due date is required'
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors)
      return
    }

    // Check if user can borrow (no outstanding fines)
    if (userInfo && !userInfo.can_borrow) {
      setErrors({ user: `User has outstanding fines of ₹${userInfo.total_fine}. Please clear fines before issuing books.` })
      return
    }

    setIsSubmitting(true)
    try {
      console.log('Issuing book with data:', {
        user_id: userInfo.user.user_id,
        book_id: selectedBook.id,
        due_date: formData.dueDate
      })

      const response = await axios.post('/admin/circulation/issue', {
        user_id: userInfo.user.user_id,
        book_id: selectedBook.id,
        due_date: formData.dueDate
      })

      console.log('Issue book response:', response.data)
      alert('Book issued successfully!')

      // Reset form
      setFormData({ userId: '', bookId: '', dueDate: dueDate })
      setUserInfo(null)
      setSelectedBook(null)
      setBookSearch('')
      setSearchResults([])

      // Refresh user info if same user
      if (userInfo) {
        handleUserSearch(userInfo.user.user_id)
      }

    } catch (error) {
      console.error('Issue book failed:', error)
      console.error('Error details:', error.response?.data)
      alert(error.response?.data?.error || 'Failed to issue book')
    } finally {
      setIsSubmitting(false)
    }
  }

  const debugBooks = async () => {
    try {
      const response = await axios.get('/admin/books/debug')
      console.log('Debug books response:', response.data)
      alert(`Total books: ${response.data.total_books}, Available: ${response.data.available_books}`)
    } catch (error) {
      console.error('Debug books failed:', error)
      alert('Failed to get book debug info')
    }
  }

  const createSampleBooks = async () => {
    try {
      const response = await axios.post('/admin/books/create-samples')
      console.log('Create sample books response:', response.data)
      alert(response.data.message)
    } catch (error) {
      console.error('Create sample books failed:', error)
      alert('Failed to create sample books: ' + (error.response?.data?.error || error.message))
    }
  }

  const debugUsers = async () => {
    try {
      const response = await axios.get('/admin/users/debug')
      console.log('Debug users response:', response.data)
      alert(`Total users: ${response.data.total_users}, Students: ${response.data.total_students}\nSample student IDs: ${response.data.sample_students.map(s => s.user_id).join(', ')}`)
    } catch (error) {
      console.error('Debug users failed:', error)
      alert('Failed to get user debug info')
    }
  }

  const createSampleStudents = async () => {
    try {
      const response = await axios.post('/admin/users/create-sample-students')
      console.log('Create sample students response:', response.data)
      alert(response.data.message + '\nStudent IDs: ' + response.data.students.join(', '))
    } catch (error) {
      console.error('Create sample students failed:', error)
      alert('Failed to create sample students: ' + (error.response?.data?.error || error.message))
    }
  }

  return (
    <div className="issue-book">
      <div className="page-header">
        <h1>Issue Book</h1>
        <p>Issue books to students and staff</p>
        <button
          type="button"
          onClick={debugBooks}
          style={{
            marginTop: '10px',
            padding: '5px 10px',
            fontSize: '0.8rem',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Debug: Check Books
        </button>
        <button
          type="button"
          onClick={createSampleBooks}
          style={{
            marginTop: '5px',
            marginLeft: '10px',
            padding: '5px 10px',
            fontSize: '0.8rem',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Create Sample Books
        </button>
        <button
          type="button"
          onClick={debugUsers}
          style={{
            marginTop: '5px',
            marginLeft: '10px',
            padding: '5px 10px',
            fontSize: '0.8rem',
            backgroundColor: '#17a2b8',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Debug: Check Users
        </button>
        <button
          type="button"
          onClick={createSampleStudents}
          style={{
            marginTop: '5px',
            marginLeft: '10px',
            padding: '5px 10px',
            fontSize: '0.8rem',
            backgroundColor: '#6f42c1',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Create Sample Students
        </button>
      </div>

      <div className="issue-book-container">
        <div className="issue-form-section">
          <div className="form-card">
            <h2>Book Issue Form</h2>

            <form onSubmit={handleSubmit}>
              {/* User Search */}
              <div className="form-group">
                <label>Student/Staff ID *</label>
                <div className="search-input">
                  <User size={16} />
                  <input
                    type="text"
                    placeholder="Enter user ID (roll number)"
                    value={formData.userId}
                    onChange={(e) => {
                      setFormData(prev => ({ ...prev, userId: e.target.value }))
                      handleUserSearch(e.target.value)
                    }}
                    className={errors.user ? 'error' : ''}
                  />
                  {userLoading && <div className="loading-spinner">Loading...</div>}
                </div>
                {errors.user && <span className="error-text">{errors.user}</span>}
              </div>

              {/* Book Search */}
              <div className="form-group">
                <label>Search Book *</label>
                <div className="search-input">
                  <Book size={16} />
                  <input
                    type="text"
                    placeholder="Search by title, author, or access number"
                    value={bookSearch}
                    onChange={(e) => handleBookSearch(e.target.value)}
                    className={errors.book ? 'error' : ''}
                  />
                </div>
                {errors.book && <span className="error-text">{errors.book}</span>}

                {/* Book Search Results */}
                {searchResults.length > 0 && (
                  <div className="search-results">
                    {searchResults.map((book) => (
                      <div
                        key={book.id}
                        className="search-result-item"
                        onClick={() => selectBook(book)}
                      >
                        <div className="book-info">
                          <strong>{book.title}</strong>
                          <span>by {book.author}</span>
                          <small>Access No: {book.access_no} | Available: {book.available_copies}</small>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Issue and Due Dates */}
              <div className="form-grid">
                <div className="form-group">
                  <label>Issue Date</label>
                  <div className="date-input">
                    <Calendar size={16} />
                    <input
                      type="date"
                      value={issueDate}
                      disabled
                    />
                  </div>
                </div>
                <div className="form-group">
                  <label>Due Date *</label>
                  <div className="date-input">
                    <Calendar size={16} />
                    <input
                      type="date"
                      value={formData.dueDate}
                      onChange={(e) => setFormData(prev => ({ ...prev, dueDate: e.target.value }))}
                      min={issueDate}
                      className={errors.dueDate ? 'error' : ''}
                    />
                  </div>
                  {errors.dueDate && <span className="error-text">{errors.dueDate}</span>}
                </div>
              </div>

              {/* Submit Button */}
              <div className="form-actions">
                <button
                  type="submit"
                  className="btn btn-primary"
                  disabled={isSubmitting || !userInfo || !selectedBook || !userInfo?.can_borrow}
                >
                  {isSubmitting ? 'Issuing...' : 'Issue Book'}
                </button>
              </div>
            </form>
          </div>
        </div>

        {/* User Information Panel */}
        {userInfo && (
          <div className="user-info-section">
            <div className="user-card">
              <div className="user-header">
                <div className="user-avatar">
                  <User size={24} />
                </div>
                <div className="user-details">
                  <h3>{userInfo.user.name}</h3>
                  <p>ID: {userInfo.user.user_id}</p>
                  <p>{userInfo.user.email}</p>
                  {userInfo.user.college && (
                    <p>{userInfo.user.college} - {userInfo.user.department}</p>
                  )}
                </div>
                <div className="user-status">
                  {userInfo.can_borrow ? (
                    <div className="status-badge success">
                      <CheckCircle size={16} />
                      Can Borrow
                    </div>
                  ) : (
                    <div className="status-badge danger">
                      <AlertCircle size={16} />
                      Has Fines
                    </div>
                  )}
                </div>
              </div>

              {/* Current Fine Amount */}
              {userInfo.total_fine > 0 && (
                <div className="fine-alert">
                  <AlertCircle size={16} />
                  <span>Outstanding Fine: ${userInfo.total_fine.toFixed(2)}</span>
                </div>
              )}

              {/* Currently Borrowed Books */}
              <div className="section">
                <h4>Currently Borrowed Books ({userInfo.current_books.length})</h4>
                {userInfo.current_books.length === 0 ? (
                  <p className="no-data">No books currently borrowed</p>
                ) : (
                  <div className="books-list">
                    {userInfo.current_books.map((item) => (
                      <div key={item.circulation_id} className="book-item">
                        <div className="book-details">
                          <strong>{item.title}</strong>
                          <span>by {item.author}</span>
                          <small>Access No: {item.access_no}</small>
                        </div>
                        <div className="book-dates">
                          <div className="date-info">
                            <span>Issued: {new Date(item.issue_date).toLocaleDateString()}</span>
                            <span>Due: {new Date(item.due_date).toLocaleDateString()}</span>
                          </div>
                          {item.is_overdue && (
                            <div className="overdue-badge">
                              <Clock size={14} />
                              Overdue ({item.days_overdue} days)
                              {item.fine_amount > 0 && (
                                <span className="fine-amount">${item.fine_amount.toFixed(2)}</span>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Borrowing History */}
              <div className="section">
                <h4>Recent Borrowing History</h4>
                {userInfo.borrowing_history.length === 0 ? (
                  <p className="no-data">No borrowing history</p>
                ) : (
                  <div className="history-list">
                    {userInfo.borrowing_history.slice(0, 5).map((item, index) => (
                      <div key={index} className="history-item">
                        <div className="book-info">
                          <strong>{item.book_title}</strong>
                          <span>by {item.author}</span>
                        </div>
                        <div className="history-dates">
                          <span>Issued: {new Date(item.issue_date).toLocaleDateString()}</span>
                          {item.return_date && (
                            <span>Returned: {new Date(item.return_date).toLocaleDateString()}</span>
                          )}
                          <span className={`status ${item.status}`}>{item.status}</span>
                          {item.fine_amount > 0 && (
                            <span className="fine">Fine: ${item.fine_amount.toFixed(2)}</span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default IssueBook
