import React, { useState } from 'react'
import { Search, User, DollarSign, CreditCard, Printer, CheckCircle, AlertCircle, Receipt } from 'lucide-react'
import axios from 'axios'

const PaymentManagement = () => {
  const [userInfo, setUserInfo] = useState(null)
  const [userLoading, setUserLoading] = useState(false)
  const [selectedFines, setSelectedFines] = useState([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [paymentSuccess, setPaymentSuccess] = useState(false)
  const [receipt, setReceipt] = useState(null)
  const [errors, setErrors] = useState({})

  const [formData, setFormData] = useState({
    userId: '',
    paymentMethod: 'cash'
  })

  const handleUserSearch = async (userId) => {
    if (!userId.trim()) {
      setUserInfo(null)
      setSelectedFines([])
      setPaymentSuccess(false)
      setReceipt(null)
      return
    }

    setUserLoading(true)
    try {
      // Get user fines using the correct admin endpoint
      const response = await axios.get(`/admin/fines/user/${userId}`)

      setUserInfo({
        user: response.data.user,
        fines: response.data.fines.filter(fine => fine.status === 'pending'),
        total_pending: response.data.total_pending
      })
      
      setFormData(prev => ({ ...prev, userId: userId }))
      setErrors(prev => ({ ...prev, user: '' }))
      setSelectedFines([])
      setPaymentSuccess(false)
      setReceipt(null)
    } catch (error) {
      setUserInfo(null)
      setSelectedFines([])
      setErrors(prev => ({ ...prev, user: error.response?.data?.error || 'User not found or no fines available' }))
    } finally {
      setUserLoading(false)
    }
  }

  const toggleFineSelection = (fine) => {
    const isSelected = selectedFines.some(f => f.id === fine.id)
    
    if (isSelected) {
      setSelectedFines(selectedFines.filter(f => f.id !== fine.id))
    } else {
      setSelectedFines([...selectedFines, fine])
    }
  }

  const selectAllFines = () => {
    if (selectedFines.length === userInfo.fines.length) {
      setSelectedFines([])
    } else {
      setSelectedFines([...userInfo.fines])
    }
  }

  const calculateTotalAmount = () => {
    return selectedFines.reduce((sum, fine) => sum + fine.amount, 0)
  }

  const handlePayment = async (e) => {
    e.preventDefault()
    
    if (!userInfo) {
      setErrors({ user: 'Please search and select a valid user' })
      return
    }

    if (selectedFines.length === 0) {
      setErrors({ fines: 'Please select at least one fine to pay' })
      return
    }

    setIsProcessing(true)
    try {
      const paymentPromises = selectedFines.map(fine => 
        axios.post(`/admin/fines/${fine.id}/pay`)
      )
      
      await Promise.all(paymentPromises)
      
      // Create receipt data
      const receiptData = {
        receiptNumber: `RCP-${Date.now()}`,
        date: new Date().toISOString(),
        user: userInfo.user,
        fines: selectedFines,
        totalAmount: calculateTotalAmount(),
        paymentMethod: formData.paymentMethod
      }
      
      setReceipt(receiptData)
      setPaymentSuccess(true)
      
      // Refresh user info
      setTimeout(() => {
        handleUserSearch(userInfo.user.user_id)
      }, 1000)
      
    } catch (error) {
      alert(error.response?.data?.error || 'Failed to process payment')
    } finally {
      setIsProcessing(false)
    }
  }

  const printReceipt = () => {
    if (!receipt) return
    
    const printWindow = window.open('', '_blank')
    const receiptHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Payment Receipt</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .receipt { max-width: 400px; margin: 0 auto; }
          .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; margin-bottom: 20px; }
          .row { display: flex; justify-content: space-between; margin: 5px 0; }
          .total { border-top: 1px solid #000; padding-top: 10px; font-weight: bold; }
          .fine-item { border-bottom: 1px dotted #ccc; padding: 5px 0; }
        </style>
      </head>
      <body>
        <div class="receipt">
          <div class="header">
            <h2>Library Management System</h2>
            <h3>Payment Receipt</h3>
          </div>
          <div class="row"><span>Receipt #:</span><span>${receipt.receiptNumber}</span></div>
          <div class="row"><span>Date:</span><span>${new Date(receipt.date).toLocaleString()}</span></div>
          <div class="row"><span>Student ID:</span><span>${receipt.user.user_id}</span></div>
          <div class="row"><span>Name:</span><span>${receipt.user.name}</span></div>
          <div class="row"><span>Email:</span><span>${receipt.user.email}</span></div>
          <br>
          <h4>Fines Paid:</h4>
          ${receipt.fines.map(fine => `
            <div class="fine-item">
              <div class="row"><span>Reason:</span><span>${fine.reason}</span></div>
              <div class="row"><span>Amount:</span><span>₹${fine.amount.toFixed(2)}</span></div>
            </div>
          `).join('')}
          <div class="total">
            <div class="row"><span>Payment Method:</span><span>${receipt.paymentMethod.toUpperCase()}</span></div>
            <div class="row"><span>Total Paid:</span><span>₹${receipt.totalAmount.toFixed(2)}</span></div>
          </div>
          <br>
          <p style="text-align: center; font-size: 12px;">Thank you for your payment!</p>
        </div>
      </body>
      </html>
    `
    
    printWindow.document.write(receiptHTML)
    printWindow.document.close()
    printWindow.print()
  }

  return (
    <div className="payment-management">
      <div className="page-header">
        <h1>Payment Management</h1>
        <p>Process fine payments and generate receipts</p>
      </div>

      <div className="payment-container">
        <div className="payment-form-section">
          <div className="form-card">
            <h2>Fine Payment</h2>
            
            <form onSubmit={handlePayment}>
              {/* User Search */}
              <div className="form-group">
                <label>Student/Staff ID *</label>
                <div className="search-input">
                  <User size={16} />
                  <input
                    type="text"
                    placeholder="Enter user ID (roll number)"
                    value={formData.userId}
                    onChange={(e) => {
                      setFormData(prev => ({ ...prev, userId: e.target.value }))
                      handleUserSearch(e.target.value)
                    }}
                    className={errors.user ? 'error' : ''}
                  />
                  {userLoading && <div className="loading-spinner">Loading...</div>}
                </div>
                {errors.user && <span className="error-text">{errors.user}</span>}
              </div>

              {/* Payment Method */}
              {userInfo && userInfo.fines.length > 0 && (
                <div className="form-group">
                  <label>Payment Method</label>
                  <select
                    value={formData.paymentMethod}
                    onChange={(e) => setFormData(prev => ({ ...prev, paymentMethod: e.target.value }))}
                  >
                    <option value="cash">Cash</option>
                    <option value="card">Card</option>
                    <option value="online">Online Transfer</option>
                    <option value="cheque">Cheque</option>
                  </select>
                </div>
              )}

              {/* Fines Selection */}
              {userInfo && userInfo.fines.length > 0 && (
                <div className="fines-selection-section">
                  <div className="section-header">
                    <h3>Select Fines to Pay</h3>
                    <button
                      type="button"
                      className="btn btn-secondary"
                      onClick={selectAllFines}
                    >
                      {selectedFines.length === userInfo.fines.length ? 'Deselect All' : 'Select All'}
                    </button>
                  </div>
                  
                  {errors.fines && <span className="error-text">{errors.fines}</span>}
                  
                  <div className="fines-list">
                    {userInfo.fines.map((fine) => (
                      <div
                        key={fine.id}
                        className={`fine-card ${selectedFines.some(f => f.id === fine.id) ? 'selected' : ''}`}
                        onClick={() => toggleFineSelection(fine)}
                      >
                        <div className="fine-header">
                          <div className="fine-checkbox">
                            <input
                              type="checkbox"
                              checked={selectedFines.some(f => f.id === fine.id)}
                              onChange={() => toggleFineSelection(fine)}
                            />
                          </div>
                          <div className="fine-amount">
                            <DollarSign size={16} />
                            ₹{fine.amount.toFixed(2)}
                          </div>
                        </div>
                        
                        <div className="fine-details">
                          <p><strong>Reason:</strong> {fine.reason}</p>
                          <p><strong>Date:</strong> {new Date(fine.created_date).toLocaleDateString()}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Payment Summary */}
              {selectedFines.length > 0 && (
                <div className="payment-summary">
                  <div className="summary-header">
                    <CreditCard size={20} />
                    <h3>Payment Summary</h3>
                  </div>
                  <div className="summary-details">
                    <div className="summary-row">
                      <span>Selected Fines:</span>
                      <span>{selectedFines.length}</span>
                    </div>
                    <div className="summary-row total">
                      <span>Total Amount:</span>
                      <span>₹{calculateTotalAmount().toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              )}

              {/* Submit Button */}
              {userInfo && userInfo.fines.length > 0 && (
                <div className="form-actions">
                  <button 
                    type="submit" 
                    className="btn btn-primary"
                    disabled={isProcessing || selectedFines.length === 0}
                  >
                    {isProcessing ? 'Processing...' : `Pay ₹${calculateTotalAmount().toFixed(2)}`}
                  </button>
                </div>
              )}
            </form>
          </div>
        </div>

        {/* User Information Panel */}
        {userInfo && (
          <div className="user-info-section">
            <div className="user-card">
              <div className="user-header">
                <div className="user-avatar">
                  <User size={24} />
                </div>
                <div className="user-details">
                  <h3>{userInfo.user.name}</h3>
                  <p>ID: {userInfo.user.user_id}</p>
                  <p>{userInfo.user.email}</p>
                  {userInfo.user.college && (
                    <p>{userInfo.user.college} - {userInfo.user.department}</p>
                  )}
                </div>
                <div className="user-status">
                  <div className="status-badge info">
                    <DollarSign size={16} />
                    ₹{userInfo.total_pending?.toFixed(2) || '0.00'}
                  </div>
                </div>
              </div>

              {/* No Fines Message */}
              {userInfo.fines.length === 0 && (
                <div className="no-fines-message">
                  <CheckCircle size={48} />
                  <h3>No Outstanding Fines</h3>
                  <p>This user has no pending fines to pay.</p>
                </div>
              )}

              {/* Payment Success */}
              {paymentSuccess && receipt && (
                <div className="payment-success">
                  <div className="success-header">
                    <CheckCircle size={24} />
                    <h3>Payment Successful!</h3>
                  </div>
                  <div className="success-details">
                    <p>Receipt #: {receipt.receiptNumber}</p>
                    <p>Amount Paid: ₹{receipt.totalAmount.toFixed(2)}</p>
                    <p>Payment Method: {receipt.paymentMethod.toUpperCase()}</p>
                  </div>
                  <div className="success-actions">
                    <button
                      className="btn btn-secondary"
                      onClick={printReceipt}
                    >
                      <Printer size={16} />
                      Print Receipt
                    </button>
                  </div>
                </div>
              )}

              {/* Fine Statistics */}
              {userInfo.fines.length > 0 && (
                <div className="fine-stats">
                  <h4>Fine Summary</h4>
                  <div className="stats-grid">
                    <div className="stat-item">
                      <span className="stat-label">Total Fines:</span>
                      <span className="stat-value">{userInfo.fines.length}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Total Amount:</span>
                      <span className="stat-value">₹{userInfo.total_pending?.toFixed(2) || '0.00'}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Selected:</span>
                      <span className="stat-value">{selectedFines.length}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Selected Amount:</span>
                      <span className="stat-value">₹{calculateTotalAmount().toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default PaymentManagement
