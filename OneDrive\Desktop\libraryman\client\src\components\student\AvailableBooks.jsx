import React, { useState, useEffect } from 'react'
import { Book, Search, Filter, Building, User, Calendar, Hash } from 'lucide-react'
import axios from 'axios'

const AvailableBooks = () => {
  const [books, setBooks] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filters, setFilters] = useState({
    category: 'all',
    availability: 'available'
  })
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const booksPerPage = 12

  useEffect(() => {
    fetchBooks()
  }, [currentPage, searchTerm, filters])

  const fetchBooks = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: currentPage,
        per_page: booksPerPage,
        search: searchTerm,
        ...filters
      })

      const response = await axios.get(`/student/books?${params.toString()}`)
      setBooks(response.data.books)
      setTotalPages(Math.ceil(response.data.total / booksPerPage))
    } catch (error) {
      console.error('Failed to fetch books:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (e) => {
    setSearchTerm(e.target.value)
    setCurrentPage(1)
  }

  const handleFilterChange = (e) => {
    const { name, value } = e.target
    setFilters(prev => ({
      ...prev,
      [name]: value
    }))
    setCurrentPage(1)
  }

  const getAvailabilityStatus = (book) => {
    if (book.available_copies > 0) {
      return { status: 'available', text: `${book.available_copies} available` }
    } else {
      return { status: 'unavailable', text: 'Not available' }
    }
  }

  return (
    <div className="available-books">
      <div className="page-header">
        <div>
          <h1>Available Books</h1>
          <p>Browse and discover books in our library collection</p>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="search-filters">
        <div className="search-bar">
          <Search size={20} />
          <input
            type="text"
            placeholder="Search books by title, author, or ISBN..."
            value={searchTerm}
            onChange={handleSearch}
          />
        </div>
        <div className="filters">
          <div className="filter-group">
            <Filter size={16} />
            <select
              name="category"
              value={filters.category}
              onChange={handleFilterChange}
            >
              <option value="all">All Categories</option>
              <option value="fiction">Fiction</option>
              <option value="non-fiction">Non-Fiction</option>
              <option value="science">Science</option>
              <option value="technology">Technology</option>
              <option value="history">History</option>
              <option value="literature">Literature</option>
            </select>
          </div>
          <div className="filter-group">
            <select
              name="availability"
              value={filters.availability}
              onChange={handleFilterChange}
            >
              <option value="all">All Books</option>
              <option value="available">Available Only</option>
            </select>
          </div>
        </div>
      </div>

      {/* Books Grid */}
      <div className="books-section">
        {loading ? (
          <div className="loading-state">
            <div className="loading-spinner"></div>
            <p>Loading books...</p>
          </div>
        ) : books.length === 0 ? (
          <div className="no-data">
            <Book size={48} />
            <h3>No Books Found</h3>
            <p>No books match your search criteria. Try adjusting your filters.</p>
          </div>
        ) : (
          <div className="books-grid">
            {books.map((book) => {
              const availability = getAvailabilityStatus(book)
              return (
                <div key={book.id} className="book-card">
                  <div className="book-header">
                    <div className="book-icon">
                      <Book size={24} />
                    </div>
                    <div className={`availability-badge ${availability.status}`}>
                      {availability.text}
                    </div>
                  </div>
                  
                  <div className="book-content">
                    <h3 className="book-title">{book.title}</h3>
                    <div className="book-details">
                      <div className="detail-item">
                        <User size={14} />
                        <span>{book.author}</span>
                      </div>
                      <div className="detail-item">
                        <Hash size={14} />
                        <span>Access No: {book.access_no}</span>
                      </div>
                      {book.isbn && (
                        <div className="detail-item">
                          <Hash size={14} />
                          <span>ISBN: {book.isbn}</span>
                        </div>
                      )}
                      <div className="detail-item">
                        <Building size={14} />
                        <span>{book.category || 'General'}</span>
                      </div>
                      <div className="detail-item">
                        <Calendar size={14} />
                        <span>Added: {new Date(book.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="book-footer">
                    <div className="copies-info">
                      <span>Total Copies: {book.number_of_copies}</span>
                      <span>Available: {book.available_copies}</span>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="pagination">
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            Previous
          </button>
          
          <div className="page-numbers">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              let pageNum
              if (totalPages <= 5) {
                pageNum = i + 1
              } else if (currentPage <= 3) {
                pageNum = i + 1
              } else if (currentPage >= totalPages - 2) {
                pageNum = totalPages - 4 + i
              } else {
                pageNum = currentPage - 2 + i
              }
              
              return (
                <button
                  key={pageNum}
                  onClick={() => setCurrentPage(pageNum)}
                  className={currentPage === pageNum ? 'active' : ''}
                >
                  {pageNum}
                </button>
              )
            })}
          </div>
          
          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
          >
            Next
          </button>
        </div>
      )}
    </div>
  )
}

export default AvailableBooks
